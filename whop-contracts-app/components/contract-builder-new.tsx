'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { getTemplateById, contractTemplates } from '@/lib/contract-templates';
import type { ContractTemplate } from '@/lib/contract-templates';
import { ContractData, ContractFormData } from '@/lib/types';
import { processContractTemplate, validateContractData, generateContractId } from '@/lib/contract-utils';
import { exportToPDF, exportToDocx } from '@/lib/export-utils';
import { InvoiceData, createWhopInvoice, generateInvoiceHTML, validateInvoiceData } from '@/lib/invoice-utils';
import { notificationService } from '@/lib/notifications';
import { showToast, copyToClipboard, ensureFullUrl } from './toast';
import { ArrowLeft, Eye, Save, Send, Download, FileText, DollarSign, Calendar, Plus, ChevronDown } from 'lucide-react';
import { Heading, Text, Button, TextField, TextArea, Switch, Calendar as FrostCalendar, useThemeContext } from 'frosted-ui';

// PDF text extraction function - calls server-side API
const extractTextFromPDF = async (file: File): Promise<string> => {
  try {
    const formData = new FormData();
    formData.append('pdf', file);

    const response = await fetch('/api/extract-pdf-text', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to extract PDF text');
    }

    const data = await response.json();
    return data.text;
  } catch (error) {
    console.error('PDF extraction error:', error);
    throw error;
  }
};

interface User {
  id: string;
  name?: string | null;
  username: string;
  bio?: string | null;
  phoneVerified: boolean;
  createdAt: number;
  profilePicture?: {
    sourceUrl?: string | null;
  } | null;
}

interface ContractBuilderProps {
  templateId?: string | null;
  user: User;
  userId: string;
  companyId?: string;
  onSave: (contract: ContractData) => void;
  onBack: () => void;
}

// Create dynamic schema based on template
const createValidationSchema = (template: any) => {
  const sellerInfoSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().email('Valid email is required'),
    address: z.string().min(1, 'Address is required'),
    phone: z.string().optional(),
    businessName: z.string().optional(),
  });

  const clientInfoSchema = z.object({
    name: z.string().min(1, 'Client name is required'),
    email: z.string().email('Valid client email is required'),
    address: z.string().optional(),
    phone: z.string().optional(),
    businessName: z.string().optional(),
  });

  const contractFieldsSchema = z.object(
    template?.fields.reduce((acc: any, field: any) => {
      if (field.required) {
        acc[field.id] = field.type === 'email' 
          ? z.string().email(`Valid ${field.label} is required`)
          : z.string().min(1, `${field.label} is required`);
      } else {
        acc[field.id] = z.string().optional();
      }
      return acc;
    }, {}) || {}
  );

  return z.object({
    templateId: z.string(),
    sellerInfo: sellerInfoSchema,
    clientInfo: clientInfoSchema,
    contractFields: contractFieldsSchema,
  });
};

export function ContractBuilderNew({ templateId = null, user, userId, companyId = 'temp_company', onSave, onBack }: ContractBuilderProps) {
  const themeContext = useThemeContext();
  const [template, setTemplate] = useState<any>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewContent, setPreviewContent] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [showInvoiceSection, setShowInvoiceSection] = useState(false);
  const [invoiceData, setInvoiceData] = useState<InvoiceData | null>(null);
  
  // State for interactive elements
  const [selectedClient, setSelectedClient] = useState<string>('Cameron Zoub');
  const [selectedContractType, setSelectedContractType] = useState<string>('Whop Design');
  const [availableTemplates, setAvailableTemplates] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ContractTemplate | null>(null);
  const [showTemplateDropdown, setShowTemplateDropdown] = useState(false);
  const [selectedTerms, setSelectedTerms] = useState<'standard' | 'custom'>('standard');
  const [enablePayments, setEnablePayments] = useState(true);
  const [contractDuration, setContractDuration] = useState<string>('30 days');
  const [selectedPricing, setSelectedPricing] = useState<'one-time' | 'recurring'>('one-time');
  const [contractValue, setContractValue] = useState<string>('10.00');
  const [recurringPeriod, setRecurringPeriod] = useState<'weekly' | 'monthly' | 'yearly'>('monthly');
  const [currency, setCurrency] = useState<string>('USD');
  const [showContractOptions, setShowContractOptions] = useState(false);
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [customerSearch, setCustomerSearch] = useState('');
  const [isAddingNewCustomer, setIsAddingNewCustomer] = useState(false);
  const [showDueDateDropdown, setShowDueDateDropdown] = useState(false);
  const [selectedDueDate, setSelectedDueDate] = useState('Due in 30 days');
  const [showCalendar, setShowCalendar] = useState(false);
  const [showCurrencyDropdown, setShowCurrencyDropdown] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [showRecurringDropdown, setShowRecurringDropdown] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [customContractContent, setCustomContractContent] = useState<string>('');
  const [showCustomUpload, setShowCustomUpload] = useState(false);

  // Customer state
  const [customers, setCustomers] = useState<Array<{id: string, name: string, email: string, address?: string, phone?: string}>>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(false);

  // Helper function to get theme-aware colors
  const getThemeColor = (lightColor: string, darkColor: string) => {
    return themeContext.appearance === 'dark' ? darkColor : lightColor;
  };

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
    customer.email.toLowerCase().includes(customerSearch.toLowerCase())
  );

  // Fetch customers from API
  const fetchCustomers = async () => {
    if (!userId) return;
    
    setLoadingCustomers(true);
    try {
      const response = await fetch(`/api/customers?authorId=${userId}`);
      const data = await response.json();
      
      if (data.success) {
        setCustomers(data.customers);
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
    } finally {
      setLoadingCustomers(false);
    }
  };

  // Save new customer
  const saveCustomer = async (name: string, email: string, address?: string) => {
    if (!userId) return;
    
    console.log('saveCustomer called with:', { name, email, address, userId });
    
    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          email,
          address,
          authorId: userId
        })
      });
      
      const data = await response.json();
      console.log('Customer API response:', data);
      
      if (data.success) {
        // Check if customer already exists to avoid duplicates
        setCustomers(prev => {
          const exists = prev.some(c => c.id === data.customer.id);
          if (exists) {
            return prev;
          }
          return [data.customer, ...prev];
        });
        return data.customer;
      } else {
        console.error('Customer save failed:', data.error);
      }
    } catch (error) {
      console.error('Error saving customer:', error);
    }
  };

  // Handle file upload for custom contract
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      console.log('Debug - File uploaded:', file.name, file.type, file.size);
      setUploadedFile(file);

      if (file.type === 'text/plain') {
        // Read text files as text
        console.log('Debug - Reading text file');
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          console.log('Debug - Text file content loaded, length:', content?.length || 0);
          console.log('Debug - Content preview:', content?.substring(0, 100) || 'No content');
          setCustomContractContent(content);
        };
        reader.onerror = (e) => {
          console.error('Debug - Error reading file:', e);
        };
        reader.readAsText(file);
      } else if (file.type === 'application/pdf') {
        // Extract text from PDF
        try {
          const pdfText = await extractTextFromPDF(file);
          setCustomContractContent(pdfText || `# ${file.name}\n\nUnable to extract text from this PDF. The file has been uploaded successfully.`);
        } catch (error) {
          console.error('Error extracting PDF text:', error);
          setCustomContractContent(`# ${file.name}\n\nError reading PDF content. The file has been uploaded successfully.`);
        }
      } else if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
        // For Word documents, show a placeholder message (would need additional library for extraction)
        setCustomContractContent(`# ${file.name}

This is a Word document contract that has been uploaded.

**File:** ${file.name}
**Size:** ${(file.size / 1024).toFixed(1)} KB
**Type:** Word Document

Note: Word document text extraction is not yet supported. Please convert to PDF or text format for full content preview.`);
      } else {
        // For other file types, try to read as text
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          setCustomContractContent(content || `# ${file.name}\n\nFile uploaded successfully.`);
        };
        reader.readAsText(file);
      }
    }
  };

  // Handle custom template selection
  const handleCustomTemplate = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowTemplateDropdown(false);
    setShowCustomUpload(true);
    setSelectedTemplate(null);
    setTemplate(null);
    setShowPreview(false); // Remove preview when custom template is selected
    setUploadedFile(null);
    setCustomContractContent('');
    setPreviewContent(''); // Clear any existing preview content

    // Update form to use custom template
    setValue('templateId', 'custom-uploaded');
  };

  // Fetch customers on mount
  useEffect(() => {
    fetchCustomers();
  }, [userId]);

  // Initialize selected template
  useEffect(() => {
    if (!selectedTemplate && availableTemplates.length > 0 && !showCustomUpload) {
      const ndaTemplate = availableTemplates.find(t => t.id === 'nda');
      const defaultTemplate = ndaTemplate || availableTemplates[0];
      if (defaultTemplate) {
        setSelectedTemplate(defaultTemplate);
        setTemplate(defaultTemplate);
        setShowPreview(true);
      }
    }
  }, [selectedTemplate, availableTemplates, showCustomUpload]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-customer-dropdown]')) {
        setShowCustomerDropdown(false);
        setIsAddingNewCustomer(false);
      }
      if (!target.closest('[data-due-date-dropdown]')) {
        setShowDueDateDropdown(false);
      }
      if (!target.closest('[data-currency-dropdown]')) {
        setShowCurrencyDropdown(false);
      }
      if (!target.closest('[data-template-dropdown]')) {
        setShowTemplateDropdown(false);
      }
      if (!target.closest('[data-recurring-dropdown]')) {
        setShowRecurringDropdown(false);
      }
    };

    if (showCustomerDropdown || showDueDateDropdown || showCurrencyDropdown || showTemplateDropdown || showRecurringDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showCustomerDropdown, showDueDateDropdown, showCurrencyDropdown, showTemplateDropdown, showRecurringDropdown]);

  // Load available templates
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        // First load hardcoded templates
        const hardcodedTemplates = contractTemplates as unknown as ContractTemplate[];
        setAvailableTemplates(hardcodedTemplates);

        // Try to load additional templates from database
        try {
          const response = await fetch('/api/templates');
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.templates) {
              // Merge database templates with hardcoded ones, avoiding duplicates
              const dbTemplates = data.templates.filter((dbTemplate: ContractTemplate) =>
                !hardcodedTemplates.some(hardcodedTemplate => hardcodedTemplate.id === dbTemplate.id)
              );
              setAvailableTemplates([...hardcodedTemplates, ...dbTemplates]);
            }
          }
        } catch (dbError) {
          console.warn('Could not load templates from database:', dbError);
        }

        if (templateId) {
          console.log('Loading template with ID:', templateId);
          // Try to find template in hardcoded templates first
          let template = hardcodedTemplates.find(t => t.id === templateId);

          // If not found, try to get from database
          if (!template) {
            try {
              const templateResponse = await fetch(`/api/templates/${templateId}`);
              if (templateResponse.ok) {
                const templateData = await templateResponse.json();
                if (templateData.success && templateData.template) {
                  template = templateData.template;
                }
              }
            } catch (templateError) {
              console.error('Error fetching template from database:', templateError);
            }
          }

          console.log('Loaded template:', template);
          if (template) {
            setTemplate(template);
            setSelectedTemplate(template);
          } else {
            console.error('Template not found:', templateId);
          }
        } else if (!showCustomUpload && !selectedTemplate) {
          // Load default template if no templateId provided and no template is already selected
          const defaultTemplate = hardcodedTemplates.find(t => t.id === 'nda') || hardcodedTemplates[0];
          if (defaultTemplate) {
            setTemplate(defaultTemplate);
            setSelectedTemplate(defaultTemplate);
          }
        }
      } catch (error) {
        console.error('Error loading templates:', error);
      }
    };

    loadTemplates();
  }, [templateId, showCustomUpload]);

  const validationSchema = createValidationSchema(template);
  
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    getValues,
    reset,
    trigger
  } = useForm<ContractFormData>({
    resolver: zodResolver(validationSchema),
    mode: 'onChange', // Enable real-time validation
    defaultValues: {
      templateId: templateId || 'service-agreement', // Use service-agreement as default template
      sellerInfo: {
        name: user.name || 'Whop Inc.',
        email: user.username,
        address: '1234 Innovation Drive, San Francisco, CA 94105',
        phone: '',
        businessName: 'Whop Inc.'
      },
      clientInfo: {
        name: '',
        email: '',
        address: '',
        phone: '',
        businessName: ''
      },
      contractFields: {}
    }
  });

  // Custom validation state to track when all required fields are filled
  const [isFormValid, setIsFormValid] = useState(false);
  
  // Watch all form values to determine if form is valid
  const formValues = watch();
  
  // Check if form is valid whenever form values or template changes
  useEffect(() => {
    const checkFormValidity = async () => {
      if (!template) return;
      
      try {
        // Validate the current form data against the schema
        const currentData = getValues();
        
        // Check if all required client info fields are filled
        const clientInfoValid = currentData.clientInfo.name && 
                               currentData.clientInfo.email;
        
        // Check if all required contract fields are filled (skip for custom contracts)
        const contractFieldsValid = template.id === 'custom-uploaded' || template.fields.every((field: any) => {
          if (!field.required) return true;
          const fieldValue = currentData.contractFields[field.id];
          return fieldValue && fieldValue.trim().length > 0;
        });
        
        // Check if template is selected (any valid template ID)
        const templateSelected = currentData.templateId && currentData.templateId.trim().length > 0;
        
        const isValid = clientInfoValid && contractFieldsValid && templateSelected;
        setIsFormValid(isValid);
      } catch (error) {
        console.error('Error checking form validity:', error);
        setIsFormValid(false);
      }
    };
    
    checkFormValidity();
  }, [formValues, template, getValues]);

  // Update form when template changes
  useEffect(() => {
    if (template) {
      // Reset form with new template
      reset({
        templateId: template.id,
        sellerInfo: {
          name: user.name || 'Whop Inc.',
          email: user.username,
          address: '1234 Innovation Drive, San Francisco, CA 94105',
          phone: '',
          businessName: 'Whop Inc.'
        },
        clientInfo: {
          name: '',
          email: '',
          address: '',
          phone: '',
          businessName: ''
        },
        contractFields: {}
      });
    }
  }, [template, reset, user.name, user.username]);

  const onSubmit = async (data: ContractFormData) => {
    try {
      const contractData: ContractData = {
        id: generateContractId(),
        templateId: data.templateId,
        sellerInfo: data.sellerInfo,
        clientInfo: data.clientInfo,
        contractFields: data.contractFields,
        status: 'DRAFT' as any,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      onSave(contractData);
    } catch (error) {
      console.error('Error saving contract:', error);
    }
  };

  const [isSending, setIsSending] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const handleGenerateContractLink = async () => {
    // Prevent multiple rapid clicks
    if (isSending) {
      return;
    }

    try {
      setIsSending(true);
      const formData = getValues();
      
      // First save the contract
      const contractData: ContractData = {
        id: generateContractId(),
        templateId: formData.templateId,
        sellerInfo: formData.sellerInfo,
        clientInfo: formData.clientInfo,
        contractFields: formData.contractFields,
        status: 'DRAFT' as any,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Prepare contract fields, including custom content if it's a custom contract
      const contractFieldsToSend = { ...formData.contractFields };

      console.log('Debug - Template ID:', formData.templateId);
      console.log('Debug - Custom content length:', customContractContent?.length || 0);
      console.log('Debug - Custom content preview:', customContractContent?.substring(0, 100) || 'No content');

      if (formData.templateId === 'custom-uploaded') {
        // Add custom content to contract fields, with fallback to template object
        if (customContractContent) {
          contractFieldsToSend.customContractContent = customContractContent;
          console.log('Debug - Added custom content to contract fields');
        } else if (template && template.template) {
          // Fallback: try to get content from the template object
          contractFieldsToSend.customContractContent = template.template;
          console.log('Debug - Using content from template object as fallback');
        }
        // Note: If no content is available, the API will return a proper error message
      }

      // Save contract to database first
      const saveResponse = await fetch('/api/contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: contractData.id,
          templateId: contractData.templateId,
          sellerInfo: contractData.sellerInfo,
          clientInfo: contractData.clientInfo,
          contractFields: contractFieldsToSend,
          authorId: userId
        })
      });

      if (!saveResponse.ok) {
        const error = await saveResponse.json();
        showToast({
          type: 'error',
          title: 'Failed to Save Contract',
          message: error.error || 'An error occurred while saving the contract.',
          duration: 5000
        });
        return;
      }

      // Call the onSave callback to update UI
      onSave(contractData);

      // Save invoice data if payment is enabled (but don't create Whop invoice yet)
      let invoiceLink: string | null = null;
      if (enablePayments && contractValue && parseFloat(contractValue) > 0) {
        console.log('Saving invoice data for contract:', contractData.id, 'Amount:', contractValue);
        try {
          const dueDate = new Date();
          dueDate.setDate(dueDate.getDate() + 30); // Default 30 days from now

          const invoicePayload = {
            contractId: contractData.id,
            amount: parseFloat(contractValue), // Ensure it's a number in dollars, not cents
            description: `Payment for Contract #${contractData.id.split('_')[1]?.substring(0, 8)}`,
            dueDate: dueDate.toISOString(),
            paymentMethod: 'Credit Card',
            companyId: companyId,
            savePendingOnly: true // Flag to indicate we only want to save data, not create Whop invoice
          };

          console.log('Invoice payload (pending only):', invoicePayload);

          const invoiceResponse = await fetch('/api/invoices', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(invoicePayload)
          });

          console.log('Invoice response status:', invoiceResponse.status);

          if (invoiceResponse.ok) {
            const invoiceResult = await invoiceResponse.json();
            console.log('Invoice data saved:', invoiceResult);
            // No checkout link yet - will be created after contract signing
          } else {
            const errorText = await invoiceResponse.text();
            console.error('Failed to save invoice data:', errorText);
          }
        } catch (invoiceError) {
          console.error('Error creating invoice:', invoiceError);
        }
      } else {
        console.log('Invoice creation skipped - payments not enabled or no amount');
      }

      // Then generate shareable link
      const response = await fetch(`/api/contracts/${contractData.id}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
      });

      if (response.ok) {
        const result = await response.json();
        const shareableLink = result.contract.shareableLink;
        
        // Copy link to clipboard
        const copied = await copyToClipboard(shareableLink);
        
        // Show success toast
        const successMessage = copied
          ? `Contract link copied to clipboard: ${ensureFullUrl(shareableLink)}`
          : `Contract link generated: ${ensureFullUrl(shareableLink)}`;

        const invoiceMessage = enablePayments && contractValue
          ? ` Payment invoice will open automatically after client signs the contract.`
          : '';

        showToast({
          type: 'success',
          title: 'Contract Link Generated!',
          message: successMessage + invoiceMessage,
          action: {
            label: 'View Contract',
            onClick: () => window.open(ensureFullUrl(shareableLink), '_blank')
          },
          duration: 6000
        });
      } else {
        const error = await response.json();
        showToast({
          type: 'error',
          title: 'Failed to Generate Contract Link',
          message: error.error || 'An error occurred while generating the contract link.',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Error sending contract:', error);
      showToast({
        type: 'error',
        title: 'Failed to Generate Contract Link',
        message: 'An unexpected error occurred. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSending(false);
    }
  };

  const handleDownloadContract = async () => {
    if (isDownloading) {
      return;
    }

    try {
      setIsDownloading(true);
      const formData = getValues();
      
      // Generate contract content for PDF
      const contractContent = generateContractContent(formData);
      
      // Export to PDF
      await exportToPDF(contractContent, {
        format: 'pdf',
        filename: `contract-${formData.templateId || 'draft'}.pdf`,
        includeWatermark: false
      });

      showToast({
        type: 'success',
        title: 'Contract Downloaded',
        message: 'Contract has been downloaded as PDF',
        duration: 3000
      });
      
    } catch (error) {
      console.error('Error downloading contract:', error);
      showToast({
        type: 'error',
        title: 'Download Failed',
        message: 'Failed to download contract PDF. Please try again.',
        duration: 5000
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const generateContractContent = (formData: any) => {
    if (!template) {
      return '<div>Loading template...</div>';
    }

    // Handle custom uploaded contracts
    if (template.id === 'custom-uploaded' && template.template) {
      // For custom contracts, process the content with form data
      let processedContent = template.template;

      // Replace seller info variables
      processedContent = processedContent.replace(/\{\{sellerInfo\.name\}\}/g, formData.sellerInfo?.name || '');
      processedContent = processedContent.replace(/\{\{sellerInfo\.email\}\}/g, formData.sellerInfo?.email || '');
      processedContent = processedContent.replace(/\{\{sellerInfo\.address\}\}/g, formData.sellerInfo?.address || '');
      processedContent = processedContent.replace(/\{\{sellerInfo\.phone\}\}/g, formData.sellerInfo?.phone || '');
      processedContent = processedContent.replace(/\{\{sellerInfo\.businessName\}\}/g, formData.sellerInfo?.businessName || '');

      // Replace client info variables
      processedContent = processedContent.replace(/\{\{clientInfo\.name\}\}/g, formData.clientInfo?.name || '');
      processedContent = processedContent.replace(/\{\{clientInfo\.email\}\}/g, formData.clientInfo?.email || '');
      processedContent = processedContent.replace(/\{\{clientInfo\.address\}\}/g, formData.clientInfo?.address || '');
      processedContent = processedContent.replace(/\{\{clientInfo\.phone\}\}/g, formData.clientInfo?.phone || '');
      processedContent = processedContent.replace(/\{\{clientInfo\.businessName\}\}/g, formData.clientInfo?.businessName || '');

      // Replace contract field placeholders
      Object.entries(formData.contractFields || {}).forEach(([key, value]) => {
        if (key !== 'customContractContent') { // Don't replace the custom content itself
          const placeholder = new RegExp(`\\{\\{contractFields\\.${key}\\}\\}`, 'g');
          processedContent = processedContent.replace(placeholder, String(value || ''));
        }
      });

      // Replace contract date
      const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      processedContent = processedContent.replace(/\{\{contract_date\}\}/g, currentDate);

      // Convert to HTML
      const htmlContent = processedContent
        .replace(/^# (.*$)/gim, '<h1 style="text-align: center; margin-bottom: 20px; font-size: 16px; font-weight: bold; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto; line-height: 1.2; max-width: 100%;">$1</h1>')
        .replace(/^## (.*$)/gim, '<h2 style="font-size: 14px; font-weight: bold; margin-bottom: 12px; margin-top: 20px; word-wrap: break-word; overflow-wrap: break-word;">$1</h2>')
        .replace(/^### (.*$)/gim, '<h3 style="font-size: 12px; font-weight: bold; margin-bottom: 8px; word-wrap: break-word; overflow-wrap: break-word;">$1</h3>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/\n/g, '<br/>');

      return `
        <div style="font-family: 'Times New Roman', serif; font-size: 12px; line-height: 1.4; color: var(--text-primary); width: 100%; box-sizing: border-box; max-width: 100%; overflow: hidden;">
          ${htmlContent}
        </div>
      `;
    }

    // Use the processContractTemplate function to generate the actual contract content
    const processedTemplate = processContractTemplate(
      template,
      formData.sellerInfo,
      formData.clientInfo,
      formData.contractFields
    );

    // Convert markdown to HTML for better display
    const htmlContent = processedTemplate
      .replace(/^# (.*$)/gim, '<h1 style="text-align: center; margin-bottom: 20px; font-size: 16px; font-weight: bold; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto; line-height: 1.2; max-width: 100%;">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 style="font-size: 14px; font-weight: bold; margin-bottom: 12px; margin-top: 20px; word-wrap: break-word; overflow-wrap: break-word;">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 style="font-size: 12px; font-weight: bold; margin-bottom: 8px; word-wrap: break-word; overflow-wrap: break-word;">$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br/>');

    return `
      <div style="font-family: 'Times New Roman', serif; font-size: 12px; line-height: 1.4; color: var(--text-primary); width: 100%; box-sizing: border-box; max-width: 100%; overflow: hidden;">
        ${htmlContent}
      </div>
    `;
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        backgroundColor: themeContext.appearance === 'dark' ? '#111111' : '#f8f9fa',
        fontFamily: 'Inter Display, sans-serif'
      }}
    >
      {/* Header */}
      <div
        style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
          padding: '10px 16px',
        backgroundColor: themeContext.appearance === 'dark' ? '#1a1a1a' : '#ffffff',
        borderBottom: `1px solid ${themeContext.appearance === 'dark' ? '#333333' : '#e5e7eb'}`
        }}
      >
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Button
            variant="ghost"
            size="2"
            onClick={onBack}
            style={{
              boxShadow: 'none',
              outline: 'none',
              background: 'none',
              padding: '4px',
              minWidth: 'unset',
              minHeight: 'unset',
              borderRadius: '6px',
              transition: 'background 0.15s'
            }}
            onMouseOver={e => {
              e.currentTarget.style.background = 'var(--hover-background)';
            }}
            onMouseOut={e => {
              e.currentTarget.style.background = 'none';
            }}
            onFocus={e => {
              e.currentTarget.style.boxShadow = '0 0 0 2px var(--border-color)';
              e.currentTarget.style.background = 'var(--hover-background)';
            }}
            onBlur={e => {
              e.currentTarget.style.boxShadow = 'none';
              e.currentTarget.style.background = 'none';
            }}
          >
            <ArrowLeft size={16} color="var(--text-secondary)" />
          </Button>
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            <Text size="2" style={{ color: 'var(--text-secondary)' }}>
              Contracts
            </Text>
            <Text size="2" style={{ color: 'var(--text-secondary)' }}>
              /
            </Text>
            <Text size="2" style={{ color: 'var(--text-primary)' }}>
              Create Contract
            </Text>
          </div>
        </div>
        <Button
          color="blue"
          size="3"
          variant="classic"
          onClick={handleGenerateContractLink}
          disabled={!isFormValid || isSending}
          style={{
            background: isFormValid
              ? 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
              : '#9ca3af',
            color: 'white',
            border: 'none',
            borderRadius: '12px',
            padding: '12px 24px',
            fontSize: '16px',
            fontWeight: '600',
            boxShadow: isFormValid
              ? '0 4px 14px 0 rgba(59, 130, 246, 0.4), 0 2px 4px 0 rgba(0, 0, 0, 0.1)'
              : '0 2px 4px 0 rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            transform: 'translateY(0)',
            cursor: isFormValid ? 'pointer' : 'not-allowed'
          }}
          onMouseEnter={(e) => {
            if (isFormValid && !isSending) {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 8px 25px 0 rgba(59, 130, 246, 0.5), 0 4px 8px 0 rgba(0, 0, 0, 0.15)';
              e.currentTarget.style.background = 'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)';
            }
          }}
          onMouseLeave={(e) => {
            if (isFormValid) {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 14px 0 rgba(59, 130, 246, 0.4), 0 2px 4px 0 rgba(0, 0, 0, 0.1)';
              e.currentTarget.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
            }
          }}
        >
          {isSending ? 'Generating...' : 'Create Contract'}
        </Button>
      </div>

      {/* Main Content */}
      <div style={{ display: 'flex', height: 'calc(100vh - 73px)' }}>
        {/* Left Panel - Form */}
        <div style={{
          width: '50%',
          backgroundColor: themeContext.appearance === 'dark' ? '#1a1a1a' : '#ffffff',
          padding: '32px',
          overflowY: 'auto',
          borderRight: `1px solid ${themeContext.appearance === 'dark' ? '#333333' : '#e5e7eb'}`,
          color: themeContext.appearance === 'dark' ? '#ffffff' : '#202020'
        }}>
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            {/* Customer Section */}
            <div style={{ marginBottom: '24px', position: 'relative' }}>
              <Text
                size="2"
                style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '500',
                  fontSize: '14px',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: 'var(--text-primary)',
                  marginBottom: '8px',
                  display: 'block'
                }}
              >
                Customer
              </Text>

              {/* Customer Input with Dropdown */}
              <div style={{ position: 'relative' }} data-customer-dropdown>
                {!isAddingNewCustomer ? (
                  <>
                    <input
                      type="text"
                      placeholder="Find or add a customer"
                      value={customerSearch}
                      onChange={(e) => {
                        setCustomerSearch(e.target.value);
                        setShowCustomerDropdown(true);
                      }}
                      onFocus={() => setShowCustomerDropdown(true)}
                        style={{
                          width: '100%',
                        height: '32px',
                        padding: '0 35px 0 10px',
                        border: '1px solid var(--input-border)',
                        borderRadius: '8px',
                        backgroundColor: 'var(--input-background)',
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '400',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: customerSearch ? 'var(--text-primary)' : 'var(--text-muted)',
                        outline: 'none',
                        boxShadow: '0px 1px 2px var(--shadow-light)',
                        boxSizing: 'border-box'
                      }}
                    />
                    <ChevronDown
                      size={16}
                      style={{
                        position: 'absolute',
                        right: '10px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        color: 'var(--text-secondary)',
                        cursor: 'pointer'
                      }}
                      onClick={() => setShowCustomerDropdown(!showCustomerDropdown)}
                    />
                  </>
                ) : (
                  <div style={{ 
                    display: 'flex', 
                    flexDirection: 'column', 
                    gap: '10px',
                    padding: '4px 0'
                  }}>
                    {/* Name Input */}
                    <input
                      type="text"
                      placeholder="Name"
                      autoFocus
                      {...register('clientInfo.name')}
                      style={{
                        width: '100%',
                        height: '36px',
                        padding: '0 12px',
                        border: '1px solid var(--input-border)',
                        borderRadius: '8px',
                        backgroundColor: 'var(--card-background)',
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '400',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: 'var(--text-primary)',
                        outline: 'none',
                        boxShadow: '0px 1px 2px var(--shadow-light)',
                        boxSizing: 'border-box',
                        transition: 'border-color 0.2s'
                      }}
                      onFocus={(e) => {
                        e.currentTarget.style.borderColor = '#3b82f6';
                        e.currentTarget.style.boxShadow = '0 0 0 3px var(--focus-ring)';
                      }}
                      onBlur={(e) => {
                        e.currentTarget.style.borderColor = 'var(--input-border)';
                        e.currentTarget.style.boxShadow = '0px 1px 2px var(--shadow-light)';
                      }}
                    />
                    
                    {/* Email Input */}
                    <input
                      type="email"
                      placeholder="Email"
                      {...register('clientInfo.email')}
                      style={{
                        width: '100%',
                        height: '36px',
                        padding: '0 12px',
                        border: '1px solid var(--input-border)',
                        borderRadius: '8px',
                        backgroundColor: 'var(--card-background)',
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '400',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: 'var(--text-primary)',
                        outline: 'none',
                        boxShadow: '0px 1px 2px var(--shadow-light)',
                        boxSizing: 'border-box',
                        transition: 'border-color 0.2s'
                      }}
                      onFocus={(e) => {
                        e.currentTarget.style.borderColor = '#3b82f6';
                        e.currentTarget.style.boxShadow = '0 0 0 3px var(--focus-ring)';
                      }}
                      onBlur={(e) => {
                        e.currentTarget.style.borderColor = 'var(--input-border)';
                        e.currentTarget.style.boxShadow = '0px 1px 2px var(--shadow-light)';
                      }}
                    />

                    {/* Action Buttons */}
                    <div style={{ display: 'flex', gap: '8px', marginTop: '2px' }}>
                      <button
                        type="button"
                        onClick={async () => {
                          const clientName = watch('clientInfo.name');
                          const clientEmail = watch('clientInfo.email');
                          
                          if (clientName && clientEmail) {
                            try {
                              const savedCustomer = await saveCustomer(clientName, clientEmail, '');
                              if (savedCustomer) {
                                setCustomerSearch(clientName);
                                setIsAddingNewCustomer(false);
                              }
                            } catch (error) {
                              console.error('Error saving customer:', error);
                              alert('Failed to save customer. Please try again.');
                            }
                          } else {
                            alert('Please fill out the name and email fields.');
                          }
                        }}
                        style={{
                          flex: 1,
                          height: '36px',
                          backgroundColor: '#3b82f6',
                          color: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          fontWeight: '500',
                          cursor: 'pointer',
                          outline: 'none',
                          transition: 'background-color 0.2s'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3b82f6'}
                      >
                        Save Customer
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setIsAddingNewCustomer(false);
                          setValue('clientInfo.name', '');
                          setValue('clientInfo.email', '');
                        }}
                        style={{
                          height: '36px',
                          padding: '0 16px',
                          backgroundColor: 'var(--card-background)',
                          color: 'var(--text-primary)',
                          border: '1px solid var(--border-color)',
                          borderRadius: '8px',
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          fontWeight: '500',
                          cursor: 'pointer',
                          outline: 'none',
                          transition: 'background-color 0.2s'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--hover-background)'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--card-background)'}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}

                {/* Customer Dropdown */}
                {showCustomerDropdown && !isAddingNewCustomer && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: '0',
                    right: '0',
                    backgroundColor: 'var(--dropdown-background)',
                    border: '1px solid var(--border-color)',
                    borderRadius: '8px',
                    boxShadow: '0px 4px 6px -1px var(--shadow-medium), 0px 2px 4px -1px var(--shadow-light)',
                    zIndex: 1000,
                    marginTop: '4px',
                    maxHeight: '300px',
                    overflowY: 'auto',
                    overflowX: 'hidden'
                  }}>
                    {/* Add New Customer Button */}
                    <div
                      style={{
                        padding: '12px 14px',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '10px',
                        borderBottom: '1px solid var(--separator)',
                        backgroundColor: 'var(--dropdown-background)',
                        transition: 'background-color 0.15s'
                      }}
                      onClick={() => {
                        setIsAddingNewCustomer(true);
                        setShowCustomerDropdown(false);
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--hover-background)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--dropdown-background)'}
                    >
                      <span style={{ 
                        fontSize: '16px',
                        fontWeight: '500',
                        color: '#000',
                        lineHeight: '1',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginBottom: '2px'
                      }}>+</span>
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: 'var(--text-primary)',
                        fontWeight: '500'
                      }}>
                        Add new customer
                      </Text>
                    </div>

                    {/* Customer list */}
                    {loadingCustomers ? (
                      <div style={{ padding: '16px', textAlign: 'center', color: 'var(--text-muted)', fontSize: '13px' }}>
                        Loading customers...
                      </div>
                    ) : filteredCustomers.length === 0 ? (
                      <div style={{ padding: '16px', textAlign: 'center', color: 'var(--text-muted)', fontSize: '13px' }}>
                        No customers found
                      </div>
                    ) : (
                      <div>
                        {filteredCustomers.map((customer, index) => (
                          <div
                            key={customer.id || index}
                            style={{
                              padding: '8px 12px',
                              cursor: 'pointer',
                              display: 'flex',
                              flexDirection: 'column',
                              gap: '4px',
                              backgroundColor: 'var(--dropdown-background)',
                              borderBottom: index < filteredCustomers.length - 1 ? '1px solid var(--separator)' : 'none',
                              transition: 'background-color 0.15s'
                            }}
                            onClick={() => {
                              setCustomerSearch(customer.name);
                              setShowCustomerDropdown(false);
                              setIsAddingNewCustomer(false);
                              // Populate form fields with selected customer
                              setValue('clientInfo.name', customer.name);
                              setValue('clientInfo.email', customer.email);
                            }}
                            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--hover-background)'}
                            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--dropdown-background)'}
                          >
                            <div style={{
                              display: 'flex',
                              flexDirection: 'row',
                              alignItems: 'center',
                              gap: '12px'
                            }}>
                              <Text style={{
                                fontSize: '14px',
                                fontFamily: 'Inter Display, sans-serif',
                                color: 'var(--text-primary)',
                                fontWeight: '500',
                                lineHeight: '1.4'
                              }}>
                                {customer.name}
                              </Text>
                              <Text style={{
                                fontSize: '13px',
                                fontFamily: 'Inter Display, sans-serif',
                                color: 'var(--text-secondary)',
                                lineHeight: '1.4'
                              }}>
                                {customer.email}
                              </Text>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    </div>
                )}
              </div>
            </div>

            {/* Contract Template Section */}
            <div style={{ marginBottom: '24px', position: 'relative' }}>
              <Text
                size="2"
                style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '400',
                  fontSize: '13px',
                  lineHeight: '18px',
                  letterSpacing: '0.01em',
                  color: 'var(--text-secondary)',
                  marginBottom: '6px',
                  display: 'block'
                }}
              >
                Template
              </Text>

              {/* Template Dropdown */}
              <div style={{ position: 'relative' }} data-template-dropdown>
                <div
                  style={{
                    height: '28px',
                    border: '1px solid rgba(0, 0, 0, 0.08)',
                    borderRadius: '6px',
                    padding: '0 30px 0 8px',
                    backgroundColor: 'rgba(248, 250, 252, 0.8)',
                    marginBottom: '6px',
                    cursor: 'pointer',
                    boxShadow: 'none',
                    display: 'flex',
                    alignItems: 'center',
                    boxSizing: 'border-box',
                    transition: 'all 0.2s ease'
                  }}
                  onClick={() => setShowTemplateDropdown(!showTemplateDropdown)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.12)';
                    e.currentTarget.style.backgroundColor = 'rgba(248, 250, 252, 1)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.08)';
                    e.currentTarget.style.backgroundColor = 'rgba(248, 250, 252, 0.8)';
                  }}
                >
                  <Text
                    style={{
                      fontSize: '13px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '400',
                      lineHeight: '18px',
                      letterSpacing: '0.01em',
                      color: '#6b7280'
                    }}
                  >
                    {showCustomUpload ? 'Custom Template' : (selectedTemplate?.name || 'Select Template')}
                  </Text>
                  <ChevronDown
                    size={14}
                    style={{
                      position: 'absolute',
                      right: '8px',
                      color: '#9ca3af'
                    }}
                  />
                </div>

                {/* Template Dropdown Menu */}
                {showTemplateDropdown && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: '0',
                    right: '0',
                    backgroundColor: 'white',
                    border: '1px solid rgba(0, 0, 0, 0.121569)',
                    borderRadius: '8px',
                    boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
                    zIndex: 1000,
                    marginTop: '4px',
                    maxHeight: '200px',
                    overflowY: 'auto'
                  }}>
                    {availableTemplates.map((template, index) => (
                      <div
                        key={template.id}
                        style={{
                          padding: '12px',
                          cursor: 'pointer',
                          borderBottom: '1px solid #f3f4f6'
                        }}
                        onClick={() => {
                          setSelectedTemplate(template);
                          setTemplate(template); // Update the template state used for form fields
                          setShowTemplateDropdown(false);
                          setValue('templateId', template.id);
                          setShowCustomUpload(false); // Reset custom template selection
                          // Show preview when template is selected
                          setShowPreview(true);
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
                      >
                        <div style={{ marginBottom: '4px' }}>
                          <Text style={{
                            fontSize: '14px',
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '500',
                            color: '#202020'
                          }}>
                            {template.name}
                          </Text>
                        </div>
                        <Text style={{
                          fontSize: '12px',
                          fontFamily: 'Inter Display, sans-serif',
                          color: '#6b7280',
                          lineHeight: '16px'
                        }}>
                          {template.description}
                        </Text>
                      </div>
                    ))}
                    
                    {/* Custom Template Option */}
                    <div
                      style={{
                        padding: '12px',
                        cursor: 'pointer',
                        borderTop: '1px solid #f3f4f6',
                        backgroundColor: '#f9fafb'
                      }}
                      onClick={handleCustomTemplate}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                    >
                      <div style={{ marginBottom: '4px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <span style={{
                          fontSize: '16px',
                          color: '#3b82f6',
                          fontWeight: 'normal'
                        }}>
                          +
                        </span>
                        <Text style={{
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          fontWeight: '500',
                          color: '#3b82f6'
                        }}>
                          Upload Custom Contract
                        </Text>
                      </div>
                      <Text style={{
                        fontSize: '12px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#6b7280',
                        lineHeight: '16px'
                      }}>
                        Upload your own contract template
                      </Text>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Due Date Section */}
            <div style={{ marginBottom: '24px', position: 'relative' }}>
              <Text
                size="2"
                style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '500',
                  fontSize: '14px',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: '#202020',
                  marginBottom: '8px',
                  display: 'block'
                }}
              >
                Due date
              </Text>

              {/* Due Date Dropdown */}
              <div style={{ position: 'relative' }} data-due-date-dropdown>
                <div
                    style={{
                    height: '32px',
                    border: '1px solid rgba(0, 0, 0, 0.121569)',
                    borderRadius: '8px',
                    padding: '0 35px 0 10px',
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    marginBottom: '8px',
                    cursor: 'pointer',
                    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                  display: 'flex',
                  alignItems: 'center',
                    boxSizing: 'border-box'
                  }}
                  onClick={() => setShowDueDateDropdown(!showDueDateDropdown)}
                  onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                  onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                >
                  <Text
                    style={{
                      fontSize: '14px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '400',
                      lineHeight: '20px',
                      letterSpacing: '0.01em',
                      color: '#202020'
                    }}
                  >
                    {selectedDueDate}
                  </Text>
                  <ChevronDown
                    size={16}
                    style={{
                      position: 'absolute',
                      right: '10px',
                      color: '#838383'
                    }}
                  />
                </div>

                {/* Due Date Dropdown Menu */}
                {showDueDateDropdown && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: '0',
                    right: '0',
                    backgroundColor: 'white',
                    border: '1px solid #E5E7EB',
                    borderRadius: '8px',
                    boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
                    zIndex: 1000,
                    marginTop: '4px',
                    maxHeight: '300px',
                    overflowY: 'auto'
                  }}>
                    {/* Today */}
                    <div
                      style={{
                        padding: '8px 12px',
                        cursor: 'pointer',
                        borderRadius: '6px',
                        display: 'flex',
                      justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                      onClick={() => {
                        setSelectedDueDate('Today');
                        setShowDueDateDropdown(false);
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#F3F4F6'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#202020',
                        fontWeight: '400'
                      }}>
                        Today
                      </Text>
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#6B7280',
                        fontWeight: '400'
                      }}>
                        {new Date().toLocaleDateString('en-US', {
                          month: '2-digit',
                          day: '2-digit',
                          year: 'numeric'
                        })}
                      </Text>
                    </div>

                    {/* Tomorrow */}
                    <div
                      style={{
                        padding: '8px 12px',
                        cursor: 'pointer',
                        borderRadius: '6px',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                      onClick={() => {
                        const tomorrow = new Date();
                        tomorrow.setDate(tomorrow.getDate() + 1);
                        setSelectedDueDate('Tomorrow');
                        setShowDueDateDropdown(false);
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#F3F4F6'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#202020',
                        fontWeight: '400'
                      }}>
                        Tomorrow
                      </Text>
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#6B7280',
                        fontWeight: '400'
                      }}>
                        {(() => {
                          const tomorrow = new Date();
                          tomorrow.setDate(tomorrow.getDate() + 1);
                          return tomorrow.toLocaleDateString('en-US', {
                            month: '2-digit',
                            day: '2-digit',
                            year: 'numeric'
                          });
                        })()}
                      </Text>
                    </div>

                    {/* Due in X days options */}
                    {[
                      { label: 'Due in 7 days', days: 7 },
                      { label: 'Due in 14 days', days: 14 },
                      { label: 'Due in 30 days', days: 30 }
                    ].map((option, index) => (
                      <div
                        key={index}
                        style={{
                          padding: '8px 12px',
                          cursor: 'pointer',
                          borderRadius: '6px',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}
                        onClick={() => {
                          setSelectedDueDate(option.label);
                          setShowDueDateDropdown(false);
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#F3F4F6'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <Text style={{
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          color: '#202020',
                          fontWeight: '400'
                        }}>
                          {option.label}
                        </Text>
                        <Text style={{
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          color: '#6B7280',
                          fontWeight: '400'
                        }}>
                          {(() => {
                            const futureDate = new Date();
                            futureDate.setDate(futureDate.getDate() + option.days);
                            return futureDate.toLocaleDateString('en-US', {
                              month: '2-digit',
                              day: '2-digit',
                              year: 'numeric'
                            });
                          })()}
                        </Text>
                      </div>
                    ))}

                    {/* Separator */}
              <div style={{
                      height: '1px',
                      backgroundColor: '#E5E7EB',
                      margin: '8px 0'
                    }} />

                    {/* Custom Date Option */}
                    <div
                      style={{
                        padding: '8px 12px',
                        cursor: 'pointer',
                        borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                        gap: '8px'
                      }}
                      onClick={() => {
                        setShowCalendar(true);
                        setShowDueDateDropdown(false);
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#F3F4F6'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <span style={{
                        fontSize: '16px',
                        color: '#6B7280',
                        fontWeight: 'normal'
                      }}>
                        +
                      </span>
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#202020',
                        fontWeight: '400'
                      }}>
                        Custom date
                      </Text>
                </div>
              </div>
                )}
            </div>

              {/* Calendar Modal */}
              {showCalendar && (
                <div style={{
                  position: 'fixed',
                  top: '0',
                  left: '0',
                  right: '0',
                  bottom: '0',
                  backgroundColor: 'rgba(0, 0, 0, 0.5)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 2000
                }}>
                  <div style={{
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    padding: '24px',
                    width: '400px',
                    maxWidth: '90vw'
                  }}>
                    <Text style={{
                      fontSize: '16px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '500',
                      color: '#202020',
                      marginBottom: '16px',
                      display: 'block'
                    }}>
                      Select Due Date
              </Text>

                    <div style={{ marginBottom: '16px' }}>
                      <FrostCalendar
                        onChange={(date) => {
                          if (date) {
                            // Convert CalendarDate to JavaScript Date
                            const jsDate = new Date(date.year, date.month - 1, date.day);
                            const formattedDate = jsDate.toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric'
                            });
                            setSelectedDueDate(`Due ${formattedDate}`);
                            setShowCalendar(false);
                          }
                        }}
                      />
                    </div>

                    <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                      <button
                        onClick={() => setShowCalendar(false)}
                    style={{
                          padding: '8px 16px',
                          border: '1px solid rgba(0, 0, 0, 0.121569)',
                          borderRadius: '6px',
                          backgroundColor: 'white',
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          fontWeight: '500',
                          color: '#6b7280',
                          cursor: 'pointer',
                          outline: 'none'
                        }}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              )}

            </div>


            {/* Description Section */}
            <div style={{ marginBottom: '24px' }}>
              <Text
                size="2"
                style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '500',
                  fontSize: '14px',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: '#202020',
                  marginBottom: '8px',
                  display: 'block'
                }}
              >
                Description
              </Text>
              <textarea
                placeholder="Add a description"
                style={{
                  width: '100%',
                  minHeight: '80px',
                  padding: '10px',
                  border: '1px solid rgba(0, 0, 0, 0.121569)',
                  borderRadius: '8px',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  fontSize: '14px',
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '400',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: 'rgba(0, 0, 0, 0.486275)',
                  outline: 'none',
                  resize: 'vertical',
                  boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#3b82f6';
                  e.target.style.color = '#202020';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = 'rgba(0, 0, 0, 0.121569)';
                  if (!e.target.value) e.target.style.color = 'rgba(0, 0, 0, 0.486275)';
                }}
              />
            </div>

            {/* Payment Toggle Section */}
            <div style={{ marginBottom: '24px' }}>
              <Text
                as="label"
                size="2"
                style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '500',
                  fontSize: '14px',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: '#202020',
                  cursor: 'pointer'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Switch
                    color="blue"
                    checked={enablePayments}
                    onCheckedChange={setEnablePayments}
                    size="1"
                  />
                  Enable payment collection
                </div>
              </Text>
            </div>

            {/* Price Section */}
            {enablePayments && (
            <div style={{
              boxSizing: 'border-box',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              padding: '20px',
              gap: '20px',
              width: '100%',
              backgroundColor: '#FFFFFF',
              border: '1px solid #E8E8E8',
              boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)',
              borderRadius: '8px',
              marginBottom: '24px'
            }}>
              {/* Price Header */}
              <div style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'flex-start',
                padding: '0px',
                gap: '16px',
                width: '100%',
                height: '24px'
              }}>
                <div style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '0px',
                  gap: '16px',
                  width: '100%',
                  height: '24px'
                }}>
                  <Text
                    style={{
                      fontFamily: '"Acid Grotesk", "Inter", sans-serif',
                      fontWeight: '500',
                      fontSize: '20px',
                      lineHeight: '28px',
                      letterSpacing: '0.01em',
                      color: '#202020',
                      height: '28px',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    {selectedPricing === 'one-time' ? `$${contractValue} / One-time` :
                     `$${contractValue} / ${recurringPeriod.charAt(0).toUpperCase() + recurringPeriod.slice(1)}`}
                  </Text>
                  <div style={{ width: '24px', height: '24px', transform: 'matrix(1, 0, 0, -1, 0, 0)' }}>
                    <ChevronDown size={16} style={{ color: 'rgba(0, 0, 0, 0.486275)' }} />
                  </div>
                </div>
              </div>

              {/* Price Type Buttons */}
              <div style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'flex-end',
                padding: '0px',
                width: '100%',
                height: '32px'
              }}>
                {/* One-time Button */}
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  padding: '0px',
                  isolation: 'isolate',
                  width: '50%',
                  height: '32px'
                }}>
                  <button
                    onClick={() => setSelectedPricing('one-time')}
                  style={{
                      boxSizing: 'border-box',
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0px 12px',
                      gap: '8px',
                      width: '100%',
                      height: '32px',
                      backgroundColor: selectedPricing === 'one-time' ? '#EBF2FF' : '#FFFFFF',
                      borderTop: selectedPricing === 'one-time' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderBottom: selectedPricing === 'one-time' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderLeft: selectedPricing === 'one-time' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderRight: 'none',
                      boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                      borderRadius: '8px 0px 0px 8px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '500',
                      fontSize: '14px',
                      lineHeight: '20px',
                      letterSpacing: '0.01em',
                      color: selectedPricing === 'one-time' ? '#265CCF' : '#202020',
                      cursor: 'pointer',
                      outline: 'none'
                  }}
                >
                  One-time
                  </button>
                </div>

                {/* Recurring Button */}
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  padding: '0px',
                  isolation: 'isolate',
                  width: '50%',
                  height: '32px'
                }}>
                  <button
                    onClick={() => setSelectedPricing('recurring')}
                  style={{
                      boxSizing: 'border-box',
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0px 12px',
                      gap: '8px',
                      width: '100%',
                      height: '32px',
                      backgroundColor: selectedPricing === 'recurring' ? '#EBF2FF' : '#FFFFFF',
                      borderTop: selectedPricing === 'recurring' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderBottom: selectedPricing === 'recurring' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderRight: selectedPricing === 'recurring' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderLeft: 'none',
                      boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                      borderRadius: '0px 8px 8px 0px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '500',
                      fontSize: '14px',
                      lineHeight: '20px',
                      letterSpacing: '0.01em',
                      color: selectedPricing === 'recurring' ? '#265CCF' : '#202020',
                      cursor: 'pointer',
                      outline: 'none'
                  }}
                >
                  Recurring
                  </button>
                </div>
              </div>



              {/* Price Input Section */}
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                padding: '0px',
                gap: '32px',
                width: '100%'
              }}>
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  padding: '0px',
                  gap: '16px',
                  width: '100%'
                }}>
                  {/* Price Input Row */}
                  <div style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'flex-end',
                    padding: '0px',
                    gap: '12px',
                    width: '100%',
                    height: '60px'
                  }}>
                    {/* Price Input with Label */}
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'flex-start',
                      padding: '0px',
                      gap: '8px',
                      flex: '1',
                      height: '60px'
                    }}>
                      <Text
                        style={{
                          fontFamily: 'Inter Display, sans-serif',
                          fontWeight: '500',
                          fontSize: '14px',
                          lineHeight: '20px',
                          letterSpacing: '0.01em',
                          color: '#202020',
                          height: '20px'
                        }}
                      >
                    Price:
                  </Text>
                  <div style={{
                    position: 'relative',
                    width: '100%',
                    height: '32px',
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    border: '1px solid rgba(0, 0, 0, 0.121569)',
                    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                    borderRadius: '8px'
                  }}>
                    <span style={{
                      paddingLeft: '10px',
                      fontFamily: '"Acid Grotesk", "Inter", sans-serif',
                      fontWeight: '500',
                      fontSize: '14px',
                      color: '#202020'
                    }}>
                      $
                    </span>
                  <input
                    type="text"
                      value={contractValue}
                      onChange={(e) => setContractValue(e.target.value)}
                      placeholder="100"
                    style={{
                        border: 'none',
                        outline: 'none',
                        backgroundColor: 'transparent',
                        fontFamily: '"Acid Grotesk", "Inter", sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        color: '#202020',
                        padding: '0 4px',
                        flex: selectedPricing === 'recurring' ? '0 0 auto' : '1',
                        width: selectedPricing === 'recurring' ? '60px' : 'auto'
                      }}
                    />
                    {selectedPricing === 'recurring' && (
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer',
                          padding: '0 8px 0 4px',
                          flex: '1',
                          justifyContent: 'flex-end'
                        }}
                        onClick={() => setShowRecurringDropdown(!showRecurringDropdown)}
                        data-recurring-dropdown
                      >
                        <span style={{
                          fontFamily: 'Inter Display, sans-serif',
                          fontSize: '14px',
                          color: '#6b7280',
                          marginRight: '4px'
                        }}>
                          /
                        </span>
                        <span style={{
                          fontFamily: 'Inter Display, sans-serif',
                          fontSize: '14px',
                          color: '#202020',
                          marginRight: '4px'
                        }}>
                          1 {recurringPeriod === 'monthly' ? 'month' : recurringPeriod === 'yearly' ? 'year' : 'week'}
                        </span>
                        <ChevronDown size={12} style={{ color: '#6b7280' }} />
                        {/* Recurring Period Dropdown */}
                        {showRecurringDropdown && (
                          <div style={{
                            position: 'absolute',
                            top: '100%',
                            right: '0',
                            backgroundColor: 'white',
                            border: '1px solid rgba(0, 0, 0, 0.121569)',
                            borderRadius: '8px',
                            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
                            zIndex: 1000,
                            marginTop: '4px',
                            minWidth: '120px'
                          }}>
                            {[
                              { value: 'weekly', label: '1 week' },
                              { value: 'monthly', label: '1 month' },
                              { value: 'yearly', label: '1 year' }
                            ].map((period, index) => (
                               <div
                                 key={index}
                                 style={{
                                   padding: '4px 12px',
                                   cursor: 'pointer',
                                   borderBottom: index < 2 ? '1px solid #f3f4f6' : 'none'
                                 }}
                                onClick={() => {
                                  setRecurringPeriod(period.value as 'weekly' | 'monthly' | 'yearly');
                                  setShowRecurringDropdown(false);
                                }}
                                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
                              >
                                <Text style={{
                                  fontSize: '14px',
                                  fontFamily: 'Inter Display, sans-serif',
                                  color: '#202020'
                                }}>
                                  {period.label}
                  </Text>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                    {/* Currency Dropdown */}
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'flex-start',
                      padding: '0px',
                      gap: '8px',
                      width: '74px',
                      height: '32px',
                      position: 'relative'
                    }} data-currency-dropdown>
                      <div
                        style={{
                          boxSizing: 'border-box',
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                          padding: '0px',
                          isolation: 'isolate',
                          width: '74px',
                          height: '32px',
                          backgroundColor: 'rgba(255, 255, 255, 0.9)',
                          border: '1px solid rgba(0, 0, 0, 0.121569)',
                          boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                          borderRadius: '8px',
                          cursor: 'pointer'
                        }}
                        onClick={() => setShowCurrencyDropdown(!showCurrencyDropdown)}
                        onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                        onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                      >
                        <div style={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                          padding: '0px 10px',
                          gap: '6px',
                          width: '74px',
                          height: '32px'
                        }}>
                          <Text style={{
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '400',
                            fontSize: '14px',
                            lineHeight: '20px',
                            letterSpacing: '0.01em',
                            color: 'rgba(0, 0, 0, 0.87451)',
                            flex: '1'
                          }}>
                            {selectedCurrency}
                          </Text>
                          <ChevronDown size={16} style={{ color: '#838383' }} />
                </div>
              </div>

                      {/* Currency Dropdown Menu */}
                      {showCurrencyDropdown && (
                        <div style={{
                          position: 'absolute',
                          top: '100%',
                          left: '0',
                          right: '0',
                          backgroundColor: 'white',
                          border: '1px solid rgba(0, 0, 0, 0.121569)',
                          borderRadius: '8px',
                          boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
                          zIndex: 1000,
                          marginTop: '4px',
                          maxHeight: '200px',
                          overflowY: 'auto'
                        }}>
                          {['USD', 'EUR', 'GBP', 'CAD', 'AUD'].map((currency, index) => (
                            <div
                              key={index}
                              style={{
                                padding: '8px 12px',
                                cursor: 'pointer',
                                borderBottom: index < 4 ? '1px solid #f3f4f6' : 'none'
                              }}
                              onClick={() => {
                                setSelectedCurrency(currency);
                                setShowCurrencyDropdown(false);
                              }}
                              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
                            >
                              <Text style={{
                                fontSize: '14px',
                                fontFamily: 'Inter Display, sans-serif',
                                color: '#202020'
                              }}>
                                {currency}
                              </Text>
              </div>
                          ))}
            </div>
                      )}


                </div>
              </div>

                  {/* Auto Price Buttons */}
                  <div style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    padding: '0px',
                    gap: '12px',
                    width: '100%'
                  }}>
                    {/* $9.99 Button */}
                    <button
                      onClick={() => setContractValue('9.99')}
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '4px 8px',
                        gap: '4px',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        border: '1px solid rgba(0, 0, 0, 0.121569)',
                        borderRadius: '8px',
                        boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                        cursor: 'pointer',
                        outline: 'none'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                      onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                    >
                      <span style={{
                        fontSize: '16px',
                        color: '#6B7280',
                        fontWeight: 'normal'
                      }}>
                        +
                      </span>
                      <Text style={{
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: '#202020'
                      }}>
                        $9.99
                      </Text>
                    </button>

                    {/* $49.99 Button */}
                    <button
                      onClick={() => setContractValue('49.99')}
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '4px 8px',
                        gap: '4px',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        border: '1px solid rgba(0, 0, 0, 0.121569)',
                        borderRadius: '8px',
                        boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                        cursor: 'pointer',
                        outline: 'none'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                      onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                    >
                      <span style={{
                        fontSize: '16px',
                        color: '#6B7280',
                        fontWeight: 'normal'
                      }}>
                        +
                      </span>
                      <Text style={{
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: '#202020'
                      }}>
                        $49.99
                      </Text>
                    </button>

                    {/* $149.99 Button */}
                    <button
                      onClick={() => setContractValue('149.99')}
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '4px 8px',
                        gap: '4px',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        border: '1px solid rgba(0, 0, 0, 0.121569)',
                        borderRadius: '8px',
                        boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                        cursor: 'pointer',
                        outline: 'none'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                      onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                    >
                      <span style={{
                        fontSize: '16px',
                        color: '#6B7280',
                        fontWeight: 'normal'
                      }}>
                        +
                      </span>
                      <Text style={{
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: '#202020'
                      }}>
                        $149.99
                      </Text>
                    </button>

                    {/* More Button */}
                    <div style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: '8px',
                      marginLeft: 'auto'
                    }}>
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 12L8 4M4 8L12 8" stroke="#3B82F6" strokeWidth="1.5" strokeLinecap="round"/>
                      </svg>
                      <Text style={{
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: '#3B82F6'
                      }}>
                        More
                      </Text>
                    </div>
                  </div>
              </div>
              </div>

            </div>
            )}

            {/* Contract Fields Section */}
            {template && template.fields && template.fields.length > 0 && template.id !== 'custom-uploaded' && (
              <div style={{ marginBottom: '24px' }}>
                <Text
                  size="2"
                  style={{
                    fontFamily: 'Inter Display, sans-serif',
                    fontWeight: '500',
                    fontSize: '14px',
                    lineHeight: '20px',
                    letterSpacing: '0.01em',
                    color: '#202020',
                    marginBottom: '16px',
                    display: 'block'
                  }}
                >
                  Contract Details
                </Text>
                
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  {template?.fields?.map((field: any) => (
                    <div key={field.id || `field-${Math.random()}`} style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '500',
                        color: '#374151'
                      }}>
                        {field.label} {field.required && <span style={{ color: 'var(--error-color)' }}>*</span>}
                      </Text>
                      {field.type === 'textarea' ? (
                        <textarea
                          placeholder={field.placeholder}
                          {...register(`contractFields.${field.id}`)}
                          required={field.required}
                          style={{
                            width: '100%',
                            minHeight: '80px',
                            padding: '10px',
                            border: '1px solid rgba(0, 0, 0, 0.121569)',
                            borderRadius: '8px',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            fontSize: '14px',
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '400',
                            lineHeight: '20px',
                            letterSpacing: '0.01em',
                            color: '#202020',
                            outline: 'none',
                            boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                            boxSizing: 'border-box',
                            resize: 'vertical'
                          }}
                        />
                      ) : (
                        <input
                          type={field.type === 'date' ? 'date' : field.type === 'number' ? 'number' : field.type === 'email' ? 'email' : 'text'}
                          placeholder={field.placeholder}
                          {...register(`contractFields.${field.id}`)}
                          required={field.required}
                          style={{
                            width: '100%',
                            height: '40px',
                            padding: '0 10px',
                            border: '1px solid rgba(0, 0, 0, 0.121569)',
                            borderRadius: '8px',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            fontSize: '14px',
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '400',
                            lineHeight: '20px',
                            letterSpacing: '0.01em',
                            color: '#202020',
                            outline: 'none',
                            boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                            boxSizing: 'border-box'
                          }}
                        />
                      )}
                      {errors.contractFields?.[field.id] && (
                        <Text style={{ color: 'var(--error-color)', fontSize: '12px', marginTop: '4px' }}>
                          {String(errors.contractFields[field.id]?.message || 'This field is required')}
                        </Text>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

          </div>
        </div>


        {/* Right Panel - Preview */}
        {!showCustomUpload && showPreview && (
        <div style={{
          width: '50%',
          backgroundColor: themeContext.appearance === 'dark' ? '#111111' : '#f8f9fa',
          padding: '24px',
          overflowY: 'auto',
          color: themeContext.appearance === 'dark' ? '#ffffff' : '#202020'
        }}>

         
          {/* Preview Content - Full Width Document */}
          <div style={{ display: 'flex', justifyContent: 'center', padding: '0 10px', overflow: 'hidden', height: '100%' }}>
            <div style={{ width: '100%', maxWidth: '600px', overflow: 'auto', height: '100%' }}>
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                
                {/* Contract Document */}
                <div style={{
                  padding: '32px 40px',
                  backgroundColor: themeContext.appearance === 'dark' ? '#1a1a1a' : '#ffffff',
                  fontFamily: 'Inter Display, sans-serif',
                  boxShadow: themeContext.appearance === 'dark'
                    ? '0 4px 6px -1px rgba(0, 0, 0, 0.6), 0 2px 4px -1px rgba(0, 0, 0, 0.4)'
                    : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                  borderRadius: '8px',
                  border: `1px solid ${themeContext.appearance === 'dark' ? '#333333' : '#e5e7eb'}`,
                  overflow: 'hidden',
                  wordWrap: 'break-word',
                  overflowWrap: 'break-word',
                  maxWidth: '100%',
                  boxSizing: 'border-box'
                }}>
                  <div 
                    dangerouslySetInnerHTML={{ 
                      __html: generateContractContent(watch())
                    }}
                  />
                </div>

                {/* Action Buttons - Outside Document */}
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '12px',
                  marginTop: '24px',
                  padding: '0 56px'
                }}>
                  <Button
                    variant="ghost"
                    size="2"
                    style={{
                      width: '100%',
                      color: 'var(--text-secondary)',
                      border: '1px solid var(--border-color)',
                      backgroundColor: 'transparent',
                      fontSize: '14px',
                      fontWeight: '400'
                    }}
                    onClick={handleDownloadContract}
                    disabled={isDownloading}
                  >
                    <Download size={14} />
                    {isDownloading ? 'Downloading...' : 'Download contract'}
                  </Button>

                  <Button
                    color="blue"
                    size="3"
                    variant="classic"
                    style={{
                      width: '100%',
                      background: isFormValid
                        ? 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
                        : '#9ca3af',
                      color: 'white',
                      border: 'none',
                      borderRadius: '12px',
                      padding: '12px 24px',
                      fontSize: '16px',
                      fontWeight: '600',
                      boxShadow: isFormValid
                        ? '0 4px 14px 0 rgba(59, 130, 246, 0.4), 0 2px 4px 0 rgba(0, 0, 0, 0.1)'
                        : '0 2px 4px 0 rgba(0, 0, 0, 0.1)',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      transform: 'translateY(0)',
                      cursor: isFormValid ? 'pointer' : 'not-allowed'
                    }}
                    onClick={handleGenerateContractLink}
                    disabled={!isFormValid || isSending}
                    onMouseEnter={(e) => {
                      if (isFormValid && !isSending) {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 8px 25px 0 rgba(59, 130, 246, 0.5), 0 4px 8px 0 rgba(0, 0, 0, 0.15)';
                        e.currentTarget.style.background = 'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (isFormValid) {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 4px 14px 0 rgba(59, 130, 246, 0.4), 0 2px 4px 0 rgba(0, 0, 0, 0.1)';
                        e.currentTarget.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
                      }
                    }}
                  >
                    {isSending ? 'Generating...' : 'Create Contract'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
        )}

        {/* Custom Template Upload Interface */}
        {showCustomUpload && (
          <div style={{
            width: '50%',
            backgroundColor: 'var(--muted-background)',
            padding: '24px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <div style={{
              padding: '40px',
              backgroundColor: 'var(--card-background)',
              borderRadius: '8px',
              textAlign: 'center',
              maxWidth: '400px',
              width: '100%',
              border: '1px solid var(--border-color)'
            }}>
              <h2 style={{ marginBottom: '20px', fontSize: '24px', color: 'var(--text-primary)' }}>Upload Contract</h2>

              <Button
                variant="ghost"
                size="2"
                style={{
                  width: '100%',
                  marginBottom: '20px',
                  color: 'var(--text-secondary)',
                  border: '1px solid var(--border-color)',
                  backgroundColor: 'transparent',
                  fontSize: '14px',
                  fontWeight: '400'
                }}
                onClick={() => document.getElementById('file-upload')?.click()}
              >
                Choose File
              </Button>

              <input
                id="file-upload"
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                onChange={handleFileUpload}
                style={{ display: 'none' }}
              />

              {uploadedFile && (
                <div style={{ marginBottom: '20px' }}>
                  <p>✓ {uploadedFile.name} uploaded successfully!</p>
                  {customContractContent && (
                    <div style={{
                      marginTop: '10px',
                      padding: '10px',
                      backgroundColor: 'var(--muted-background)',
                      border: '1px solid var(--border-color)',
                      borderRadius: '6px',
                      maxHeight: '200px',
                      overflowY: 'auto'
                    }}>
                      <p style={{ fontSize: '12px', color: 'var(--text-secondary)', marginBottom: '8px' }}>
                        Content Preview ({customContractContent.length} characters):
                      </p>
                      <pre style={{
                        fontSize: '11px',
                        lineHeight: '1.4',
                        margin: 0,
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-word',
                        color: 'var(--text-primary)'
                      }}>
                        {customContractContent.substring(0, 500)}
                        {customContractContent.length > 500 && '...'}
                      </pre>
                    </div>
                  )}
                  {!customContractContent && (
                    <div style={{
                      marginTop: '10px',
                      padding: '10px',
                      backgroundColor: '#fef3c7',
                      border: '1px solid #f59e0b',
                      borderRadius: '6px'
                    }}>
                      <p style={{ fontSize: '12px', color: '#92400e', margin: 0 }}>
                        ⚠️ No content loaded from file. Please try uploading again.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Use Contract Button */}
              {uploadedFile && (
                <Button
                  variant="ghost"
                  size="2"
                  style={{
                    width: '100%',
                    color: 'var(--text-secondary)',
                    border: '1px solid var(--border-color)',
                    backgroundColor: 'transparent',
                    fontSize: '14px',
                    fontWeight: '400'
                  }}
                  onClick={() => {
                    console.log('Debug - Use This Contract clicked');
                    console.log('Debug - Custom content available:', !!customContractContent);
                    console.log('Debug - Custom content length:', customContractContent?.length || 0);

                    if (!customContractContent) {
                      alert('No contract content available. Please upload a file first.');
                      return;
                    }

                    // Create a custom template object
                    const customTemplate: ContractTemplate = {
                      id: 'custom-uploaded',
                      name: 'Custom Contract',
                      description: 'Uploaded custom contract',
                      category: 'Custom',
                      template: customContractContent,
                      fields: [] // No dynamic fields for custom contracts
                    };

                    console.log('Debug - Created custom template with content length:', customTemplate.template.length);
                    setSelectedTemplate(customTemplate);
                    setTemplate(customTemplate);
                    setValue('templateId', 'custom-uploaded');
                    setShowCustomUpload(false);
                    setShowPreview(true);
                  }}
                >
                  Use This Contract
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
