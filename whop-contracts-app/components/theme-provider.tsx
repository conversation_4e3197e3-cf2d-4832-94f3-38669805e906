'use client';

import { useEffect, useState } from 'react';
import { useThemeContext } from 'frosted-ui';

export function ThemeSync() {
  const [mounted, setMounted] = useState(false);
  const themeContext = useThemeContext();

  // Apply theme to document based on frosted-ui theme context
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const applyThemeToDocument = (theme: string) => {
      const root = document.documentElement;
      const body = document.body;

      // Remove existing theme classes
      root.classList.remove('light', 'dark');
      body.classList.remove('light', 'dark');

      // Add new theme class
      root.classList.add(theme);
      body.classList.add(theme);

      // Set data attributes for CSS selectors
      root.setAttribute('data-theme', theme);
      body.setAttribute('data-theme', theme);

      // Force a style recalculation
      root.style.colorScheme = theme;

      console.log('Theme applied:', theme, 'to', root.className);
    };

    // Apply the current theme from frosted-ui
    if (themeContext.appearance) {
      applyThemeToDocument(themeContext.appearance);
    } else {
      // Fallback to system preference if frosted-ui theme is not set
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      applyThemeToDocument(systemTheme);
    }
  }, [mounted, themeContext.appearance]);

  return null;
}