'use client';

import { useState, useEffect } from 'react';
import { ContractBuilderNew as ContractBuilder } from './contract-builder-new';
import { HomePageNoContracts } from './home-page-no-contracts';
import { TemplateManager } from './template-manager';
import { ContractData } from '@/lib/types';
import { notificationService } from '@/lib/notifications';
import { initializeDemoNotifications, simulateContractActivity } from '@/lib/demo-data';
import { showToast } from './toast';
import { exportToPDF } from '@/lib/export-utils';
import { Plus, FileText, Download, Search, CheckCircle, Clock, AlertCircle, Layout, ChevronLeft, ChevronRight } from 'lucide-react';
import {
  Heading,
  Button,
  Table,
  DropdownMenu
} from 'frosted-ui';

interface User {
  id: string;
  name?: string | null;
  username: string;
  bio?: string | null;
  phoneVerified: boolean;
  createdAt: number;
  profilePicture?: {
    sourceUrl?: string | null;
  } | null;
}

interface Company {
  id: string;
  title: string;
}

interface ContractsDashboardProps {
  user: User;
  company: Company;
  companyId: string;
  userId: string;
}

type View = 'overview' | 'builder' | 'home' | 'templates';

export function ContractsDashboard({ user, company, companyId, userId }: ContractsDashboardProps) {
  const [contracts, setContracts] = useState<ContractData[]>([]);
  const [manualView, setManualView] = useState<View | null>(null);
  const [isLoadingContracts, setIsLoadingContracts] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Filter and sort contracts
  const filteredContracts = contracts.filter(contract => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!(
        contract.clientInfo.name?.toLowerCase().includes(query) ||
        contract.clientInfo.email.toLowerCase().includes(query) ||
        contract.id.toLowerCase().includes(query)
      )) {
        return false;
      }
    }

    // Status filter
    if (statusFilter !== 'all') {
      const statusMap: { [key: string]: string[] } = {
        'open': ['SENT'],
        'paid': ['SIGNED', 'COMPLETED'],
        'draft': ['DRAFT'],
        'void': ['VOID', 'CANCELLED']
      };
      const matchingStatuses = statusMap[statusFilter] || [];
      if (!matchingStatuses.includes(contract.status)) {
        return false;
      }
    }

    return true;
  }).sort((a, b) => {
    // Sort by created date
    return sortOrder === 'desc'
      ? new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      : new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
  });

  // Calculate pagination
  const totalPages = Math.ceil(filteredContracts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedContracts = filteredContracts.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, statusFilter, sortOrder]);

  // Initialize demo notifications and load contracts on component mount
  useEffect(() => {
    initializeDemoNotifications();
    loadContracts();
  }, []);

  const loadContracts = async () => {
    try {
      setIsLoadingContracts(true);
      const response = await fetch(`/api/contracts?authorId=${userId}`);
      if (response.ok) {
        const data = await response.json();
        setContracts(data.contracts || []);
      } else {
        console.error('Failed to load contracts');
      }
    } catch (error) {
      console.error('Error loading contracts:', error);
    } finally {
      setIsLoadingContracts(false);
    }
  };

  // Determine current view: if no contracts exist and no manual view is set, show home page
  // Otherwise, use manual view or default to overview
  const currentView: View = manualView || (isLoadingContracts ? 'overview' : (contracts.length === 0 ? 'home' : 'overview'));



  const handleContractSave = async (contract: ContractData) => {
    try {

      // Validate required fields before sending
      if (!contract.templateId) {
        alert('Template ID is missing. Please select a template.');
        return;
      }
      if (!contract.sellerInfo || !contract.sellerInfo.name || !contract.sellerInfo.email) {
        alert('Seller information is missing. Please fill out seller details.');
        return;
      }
      if (!contract.clientInfo || !contract.clientInfo.name || !contract.clientInfo.email) {
        alert('Client information is missing. Please fill out client details.');
        return;
      }
      if (!userId) {
        alert('User ID is missing. Please refresh the page and try again.');
        return;
      }

      // Save contract to database
      const response = await fetch('/api/contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: contract.id,
          templateId: contract.templateId,
          sellerInfo: contract.sellerInfo,
          clientInfo: contract.clientInfo,
          contractFields: contract.contractFields || {},
          authorId: userId
        })
      });

      if (!response.ok) {
        const error = await response.json();
        console.error('Failed to save contract:', error);
        showToast({
          type: 'error',
          title: 'Failed to Save Contract',
          message: error.error || 'An error occurred while saving the contract.',
          duration: 5000
        });
        return;
      }

      // Refresh contracts from database
      loadContracts();
      
      // Add notification for contract creation
      notificationService.addNotification({
        type: 'contract_viewed',
        title: 'Contract Created',
        message: `Contract for ${contract.clientInfo.name} has been created successfully`,
        contractId: contract.id,
        clientEmail: contract.clientInfo.email,
      });

      // Simulate contract activity for demo purposes
      simulateContractActivity(contract.id, contract.clientInfo.email);
      
      setManualView('overview');
    } catch (error) {
      console.error('Error saving contract:', error);
      showToast({
        type: 'error',
        title: 'Failed to Save Contract',
        message: 'An unexpected error occurred. Please try again.',
        duration: 5000
      });
    }
  };

  const handleDownloadContract = async (contractId: string) => {
    try {
      // Fetch the contract content from the download endpoint
      const response = await fetch(`/api/contracts/${contractId}/download`, {
        method: 'GET',
      });

      if (response.ok) {
        const data = await response.json();

        if (data.success && data.content) {
          // Use the existing client-side PDF generation
          await exportToPDF(data.content, {
            format: 'pdf',
            filename: `contract-${contractId}.pdf`,
            includeWatermark: false
          });

          showToast({
            title: 'Success',
            message: 'Contract downloaded successfully',
            type: 'success',
            duration: 3000
          });
        } else {
          throw new Error(data.error || 'Invalid response format');
        }
      } else {
        const error = await response.json();
        console.error('Download failed:', error);
        showToast({
          title: 'Download Failed',
          message: error.error || 'Failed to download contract',
          type: 'error',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Download error:', error);
      showToast({
        title: 'Download Error',
        message: 'An error occurred while downloading the contract',
        type: 'error',
        duration: 5000
      });
    }
  };

  const renderContent = () => {
    console.log('Current view:', currentView);
    switch (currentView) {
      case 'home':
        return (
          <HomePageNoContracts
            user={user}
            company={company}
            onCreateContract={() => {
              setSelectedTemplateId(null); // Reset template ID for new contract
              setManualView('builder');
            }}
          />
        );
      case 'builder':
        return (
          <ContractBuilder
            templateId={selectedTemplateId}
            user={user}
            userId={userId}
            companyId={companyId}
            onSave={handleContractSave}
            onBack={() => {
              setSelectedTemplateId(null); // Reset template ID when going back
              setManualView(contracts.length > 0 ? 'overview' : 'home');
            }}
          />
        );
      case 'templates':
        return (
          <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '24px' }}>
              <Button
                variant="soft"
                onClick={() => setManualView(contracts.length > 0 ? 'overview' : 'home')}
                style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
              >
                ← Back to {contracts.length > 0 ? 'Overview' : 'Home'}
              </Button>
            </div>
            <TemplateManager
              userId={userId}
              onTemplateSelect={(templateId) => {
                // Store the selected template ID and navigate to builder
                setSelectedTemplateId(templateId);
                setManualView('builder');
              }}
            />
          </div>
        );
      default:
        return (
          <div style={{
            minHeight: '100vh',
            backgroundColor: '#ffffff',
            fontFamily: 'Inter Display, sans-serif'
          }}>
            <div style={{
              width: '100%',
              padding: '32px 24px'
            }}>
              {/* Header */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '40px'
              }}>
              <div>
                <Heading
                  size="8"
                  style={{
                    fontFamily: 'Inter Display, sans-serif',
                    fontWeight: '600',
                    color: 'var(--text-primary)',
                    fontSize: '32px',
                    lineHeight: '40px',
                    letterSpacing: '-0.02em',
                    margin: 0
                  }}
                >
                  Contracts
                </Heading>
              </div>

              <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                <Button
                  color="gray"
                  size="2"
                  variant="soft"
                  onClick={() => setManualView('templates')}
                  style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
                >
                  <Layout size={16} />
                  Templates
                </Button>
                <Button
                  color="blue"
                  size="2"
                  variant="classic"
                  onClick={() => {
                    setSelectedTemplateId(null); // Reset template ID for new contract
                    setManualView('builder');
                  }}
                  style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
                >
                  <Plus size={16} />
                  Create Contract
                </Button>

              </div>

            </div>



            {/* Search and Filters */}
            <div style={{
              display: 'flex',
              gap: '16px',
              marginBottom: '24px',
              alignItems: 'center',
              flexWrap: 'wrap'
            }}>
             <div style={{ position: 'relative', minWidth: '300px' }}>
                <Search
                  size={16}
                  style={{
                    position: 'absolute',
                    left: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: '#6b7280'
                  }}
                />
                <input
                  type="text"
                  placeholder="Search contracts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  style={{
                    width: '100%',
                    paddingLeft: '40px',
                    paddingRight: '12px',
                    paddingTop: '8px',
                    paddingBottom: '8px',
                    border: 'none',
                    borderRadius: '20px',
                    fontSize: '13px',
                    outline: 'none',
                    backgroundColor: '#f8fafc',
                    fontFamily: 'system-ui, -apple-system, sans-serif',
                    fontWeight: '500',
                    color: '#64748b'
                  }}
                />
              </div>

              <DropdownMenu.Root>
                <DropdownMenu.Trigger>
                  <Button
                    variant="surface"
                    size="2"
                    style={{
                      border: 'none',
                      borderRadius: '20px',
                      fontSize: '13px',
                      fontWeight: '500',
                      color: '#64748b',
                      backgroundColor: '#f8fafc',
                      fontFamily: 'system-ui, -apple-system, sans-serif',
                      padding: '8px 12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}
                  >
                    <Plus size={16} strokeWidth={2.5} />
                    {statusFilter === 'all' ? 'Status' : statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
                  </Button>
                </DropdownMenu.Trigger>
                <DropdownMenu.Content size="2" style={{ minWidth: '160px' }}>
                  <DropdownMenu.Label style={{ fontSize: '12px', fontWeight: '500', color: '#64748b' }}>
                    Status
                  </DropdownMenu.Label>
                  <DropdownMenu.Separator />
                  <DropdownMenu.Item onClick={() => setStatusFilter('all')}>
                    All Statuses
                  </DropdownMenu.Item>
                  <DropdownMenu.Item onClick={() => setStatusFilter('open')}>
                    Open
                  </DropdownMenu.Item>
                  <DropdownMenu.Item onClick={() => setStatusFilter('paid')}>
                    Paid
                  </DropdownMenu.Item>
                  <DropdownMenu.Item onClick={() => setStatusFilter('void')}>
                    Void
                  </DropdownMenu.Item>
                  <DropdownMenu.Item onClick={() => setStatusFilter('draft')}>
                    Draft
                  </DropdownMenu.Item>
                </DropdownMenu.Content>
              </DropdownMenu.Root>

              <DropdownMenu.Root>
                <DropdownMenu.Trigger>
                  <Button
                    variant="surface"
                    size="2"
                    style={{
                      border: 'none',
                      borderRadius: '20px',
                      fontSize: '13px',
                      fontWeight: '500',
                      color: '#64748b',
                      backgroundColor: '#f8fafc',
                      fontFamily: 'system-ui, -apple-system, sans-serif',
                      padding: '8px 12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}
                  >
                    <Plus size={16} strokeWidth={2.5} />
                    {sortOrder === 'desc' ? 'Newest First' : 'Oldest First'}
                  </Button>
                </DropdownMenu.Trigger>
                <DropdownMenu.Content size="2" style={{ minWidth: '160px' }}>
                  <DropdownMenu.Label style={{ fontSize: '12px', fontWeight: '500', color: '#64748b' }}>
                    Sort by Created Date
                  </DropdownMenu.Label>
                  <DropdownMenu.Separator />
                  <DropdownMenu.Item onClick={() => setSortOrder('desc')}>
                    Newest First
                  </DropdownMenu.Item>
                  <DropdownMenu.Item onClick={() => setSortOrder('asc')}>
                    Oldest First
                  </DropdownMenu.Item>
                </DropdownMenu.Content>
              </DropdownMenu.Root>
            </div>

            {/* Separator */}
            <div style={{
              height: '1px',
              backgroundColor: '#e2e8f0',
              margin: '24px 0'
            }} />

            {/* Contracts Table */}
            {contracts.length > 0 && (
              <div style={{ marginBottom: '32px' }}>
                <div style={{
                  width: '100%'
                }}>
                  <Table.Root size="2" variant="ghost" style={{
                    width: '100%',
                    backgroundColor: 'transparent',
                    border: 'none'
                  }}>
                    <Table.Table>
                      <Table.Header>
                        <Table.Row style={{ borderBottom: 'none' }}>
                          <Table.ColumnHeaderCell style={{
                            width: '15%',
                            paddingLeft: '0',
                            paddingTop: '16px',
                            paddingBottom: '16px',
                            fontSize: '14px',
                            fontWeight: '500',
                            color: '#64748b',
                            fontFamily: 'system-ui, -apple-system, sans-serif'
                          }}>
                            Status
                          </Table.ColumnHeaderCell>
                          <Table.ColumnHeaderCell style={{
                            width: '20%',
                            paddingTop: '16px',
                            paddingBottom: '16px',
                            fontSize: '14px',
                            fontWeight: '500',
                            color: '#64748b',
                            fontFamily: 'system-ui, -apple-system, sans-serif'
                          }}>
                            Contract number
                          </Table.ColumnHeaderCell>
                          <Table.ColumnHeaderCell style={{
                            width: '25%',
                            paddingTop: '16px',
                            paddingBottom: '16px',
                            fontSize: '14px',
                            fontWeight: '500',
                            color: '#64748b',
                            fontFamily: 'system-ui, -apple-system, sans-serif'
                          }}>
                            Customer name
                          </Table.ColumnHeaderCell>
                          <Table.ColumnHeaderCell style={{
                            width: '40%',
                            paddingTop: '16px',
                            paddingBottom: '16px',
                            fontSize: '14px',
                            fontWeight: '500',
                            color: '#64748b',
                            fontFamily: 'system-ui, -apple-system, sans-serif'
                          }}>
                            Email
                          </Table.ColumnHeaderCell>
                          <Table.ColumnHeaderCell style={{
                            width: '15%',
                            textAlign: 'right',
                            paddingRight: '0',
                            paddingTop: '16px',
                            paddingBottom: '16px'
                          }}>

                          </Table.ColumnHeaderCell>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {paginatedContracts.map((contract) => {
                          const getStatusColor = (status: string) => {
                            switch (status) {
                              case 'SIGNED':
                              case 'COMPLETED':
                                return 'green';
                              case 'SENT':
                                return 'blue';
                              case 'DRAFT':
                                return 'gray';
                              default:
                                return 'red';
                            }
                          };

                          const getStatusText = (status: string) => {
                            switch (status) {
                              case 'SIGNED':
                                return 'Paid';
                              case 'COMPLETED':
                                return 'Paid';
                              case 'SENT':
                                return 'Open';
                              case 'DRAFT':
                                return 'Draft';
                              default:
                                return 'Void';
                            }
                          };

                          const getStatusIcon = (status: string) => {
                            switch (status) {
                              case 'SIGNED':
                              case 'COMPLETED':
                                return <CheckCircle size={12} />;
                              case 'SENT':
                                return <Clock size={12} />;
                              case 'DRAFT':
                                return <FileText size={12} />;
                              default:
                                return <AlertCircle size={12} />;
                            }
                          };

                          return (
                            <Table.Row key={contract.id} style={{ borderBottom: 'none' }}>
                              <Table.Cell style={{
                                paddingLeft: '0',
                                paddingTop: '16px',
                                paddingBottom: '16px'
                              }}>
                                <Button
                                  variant="soft"
                                  color={getStatusColor(contract.status)}
                                  size="1"
                                  style={{
                                    borderRadius: '20px',
                                    fontWeight: '500',
                                    fontSize: '13px',
                                    padding: '6px 12px',
                                    border: 'none',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '6px',
                                    fontFamily: 'system-ui, -apple-system, sans-serif'
                                  }}
                                >
                                  {getStatusIcon(contract.status)}
                                  {getStatusText(contract.status)}
                                </Button>
                              </Table.Cell>
                              <Table.Cell style={{
                                paddingTop: '16px',
                                paddingBottom: '16px'
                              }}>
                                <span style={{
                                  fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                                  color: '#475569',
                                  fontSize: '14px',
                                  fontWeight: '500'
                                }}>
                                  #{String(contract.id.split('_')[1] || '00000000').substring(0, 8).padStart(8, '0')}
                                </span>
                              </Table.Cell>
                              <Table.Cell style={{
                                paddingTop: '16px',
                                paddingBottom: '16px'
                              }}>
                                <span style={{
                                  color: '#334155',
                                  fontSize: '14px',
                                  fontWeight: '500',
                                  fontFamily: 'system-ui, -apple-system, sans-serif'
                                }}>
                                  {contract.clientInfo.name || '—'}
                                </span>
                              </Table.Cell>
                              <Table.Cell style={{
                                paddingTop: '16px',
                                paddingBottom: '16px'
                              }}>
                                <span style={{
                                  color: '#334155',
                                  fontSize: '14px',
                                  fontWeight: '400',
                                  fontFamily: 'system-ui, -apple-system, sans-serif'
                                }}>
                                  {contract.clientInfo.email}
                                </span>
                              </Table.Cell>
                              <Table.Cell style={{
                                textAlign: 'right',
                                paddingTop: '16px',
                                paddingBottom: '16px',
                                paddingRight: '0',
                                paddingLeft: '0'
                              }}>
                                {contract.status === 'SIGNED' && (
                                  <div style={{ marginRight: '16px', display: 'inline-block' }}>
                                    <Button
                                      variant="surface"
                                      size="1"
                                      style={{
                                        padding: '6px',
                                        minWidth: '32px',
                                        height: '32px',
                                        borderRadius: '6px',
                                        border: '1px solid #e2e8f0',
                                        backgroundColor: 'transparent',
                                        color: '#64748b',
                                        boxShadow: 'none',
                                        transition: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                                        transform: 'scale(1)',
                                        cursor: 'pointer'
                                      }}
                                      onMouseDown={(e) => {
                                        e.currentTarget.style.transform = 'scale(0.92)';
                                        e.currentTarget.style.backgroundColor = '#f1f5f9';
                                        e.currentTarget.style.borderColor = '#94a3b8';
                                        e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
                                      }}
                                      onMouseUp={(e) => {
                                        e.currentTarget.style.transform = 'scale(1)';
                                        e.currentTarget.style.backgroundColor = 'transparent';
                                        e.currentTarget.style.borderColor = '#e2e8f0';
                                        e.currentTarget.style.boxShadow = 'none';
                                      }}
                                      onMouseLeave={(e) => {
                                        e.currentTarget.style.transform = 'scale(1)';
                                        e.currentTarget.style.backgroundColor = 'transparent';
                                        e.currentTarget.style.borderColor = '#e2e8f0';
                                        e.currentTarget.style.boxShadow = 'none';
                                      }}
                                      onMouseEnter={(e) => {
                                        e.currentTarget.style.backgroundColor = '#f8fafc';
                                        e.currentTarget.style.borderColor = '#cbd5e1';
                                        e.currentTarget.style.transform = 'scale(1.02)';
                                      }}
                                      onClick={() => handleDownloadContract(contract.id)}
                                    >
                                      <Download size={14} />
                                    </Button>
                                  </div>
                                )}
                              </Table.Cell>
                            </Table.Row>
                          );
                        })}
                      </Table.Body>
                    </Table.Table>
                    <Table.BottomBar style={{
                      paddingLeft: '0',
                      paddingRight: '0',
                      paddingTop: '16px',
                      paddingBottom: '16px',
                      borderTop: '1px solid #f1f5f9',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <span style={{
                        fontSize: '13px',
                        color: '#64748b',
                        fontWeight: '400',
                        fontFamily: 'system-ui, -apple-system, sans-serif'
                      }}>
                        Showing {startIndex + 1} to {Math.min(endIndex, filteredContracts.length)} of {filteredContracts.length} contracts
                      </span>
                      
                      {totalPages > 1 && (
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px'
                        }}>
                          <Button
                            variant="soft"
                            size="1"
                            disabled={currentPage === 1}
                            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '4px',
                              padding: '6px 12px',
                              fontSize: '13px',
                              fontWeight: '500',
                              opacity: currentPage === 1 ? 0.5 : 1,
                              cursor: currentPage === 1 ? 'not-allowed' : 'pointer'
                            }}
                          >
                            <ChevronLeft size={14} />
                            Previous
                          </Button>
                          
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px'
                          }}>
                            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                              // Show first page, last page, current page, and pages around current
                              const showPage = page === 1 || 
                                              page === totalPages || 
                                              (page >= currentPage - 1 && page <= currentPage + 1);
                              
                              const showEllipsis = (page === 2 && currentPage > 3) || 
                                                  (page === totalPages - 1 && currentPage < totalPages - 2);

                              if (showEllipsis) {
                                return (
                                  <span key={page} style={{
                                    padding: '0 4px',
                                    color: '#94a3b8',
                                    fontSize: '13px'
                                  }}>
                                    ...
                                  </span>
                                );
                              }

                              if (!showPage) return null;

                              return (
                                <Button
                                  key={page}
                                  variant={currentPage === page ? 'classic' : 'soft'}
                                  color={currentPage === page ? 'blue' : 'gray'}
                                  size="1"
                                  onClick={() => setCurrentPage(page)}
                                  style={{
                                    minWidth: '32px',
                                    padding: '6px',
                                    fontSize: '13px',
                                    fontWeight: currentPage === page ? '600' : '500'
                                  }}
                                >
                                  {page}
                                </Button>
                              );
                            })}
                          </div>
                          
                          <Button
                            variant="soft"
                            size="1"
                            disabled={currentPage === totalPages}
                            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '4px',
                              padding: '6px 12px',
                              fontSize: '13px',
                              fontWeight: '500',
                              opacity: currentPage === totalPages ? 0.5 : 1,
                              cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'
                            }}
                          >
                            Next
                            <ChevronRight size={14} />
                          </Button>
                        </div>
                      )}
                    </Table.BottomBar>
                  </Table.Root>
                </div>
              </div>
            )}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--background)' }}>
      <div className="">
        {renderContent()}
      </div>
    </div>
  );
}