'use client';

import { useState, useEffect } from 'react';
import { Sun, Moon } from 'lucide-react';
import { Button } from 'frosted-ui';
import { useThemeContext } from 'frosted-ui';

export function ThemeToggle() {
  const [mounted, setMounted] = useState(false);
  const themeContext = useThemeContext();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size="2"
        className="ml-2"
        disabled
      >
        <div className="h-4 w-4" />
      </Button>
    );
  }

  const toggleTheme = () => {
    const newTheme = themeContext.appearance === 'light' ? 'dark' : 'light';
    themeContext.onAppearanceChange(newTheme);
  };

  return (
    <Button
      variant="ghost"
      size="2"
      onClick={toggleTheme}
      className="ml-2"
      title={`Switch to ${themeContext.appearance === 'light' ? 'dark' : 'light'} mode`}
    >
      {themeContext.appearance === 'light' ? (
        <Moon className="h-4 w-4" />
      ) : (
        <Sun className="h-4 w-4" />
      )}
    </Button>
  );
}