'use client';

import { useState, useEffect } from 'react';
import { ContractData } from '../lib/types';
import { DigitalSignature } from './digital-signature';
import { Download, FileText, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { getTemplateById } from '../lib/contract-templates';
import { processContractTemplate, convertMarkdownToHTML, convertDecimalToNumber } from '../lib/contract-utils';
import { Button, Text, Checkbox } from 'frosted-ui';
import { showToast } from './toast';

interface ContractViewerProps {
  contractId: string;
  onContractSigned: (signedContractId: string, signatureData: string, signingResponse?: any) => void;
  onDownloadPDF: (contractId: string) => void;
}

export function ContractViewer({ contractId, onContractSigned, onDownloadPDF }: ContractViewerProps) {
  const [contract, setContract] = useState<ContractData | null>(null);
  const [contractContent, setContractContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSigning, setIsSigning] = useState(false);
  const [showSignature, setShowSignature] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [signatureData, setSignatureData] = useState<string | null>(null);

  useEffect(() => {
    fetchContract();
  }, [contractId]);

  const fetchContract = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('Fetching contract with ID:', contractId);
      const response = await fetch(`/api/contracts/${contractId}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Contract fetch failed:', response.status, errorData);
        throw new Error(errorData.error || 'Contract not found');
      }
      
      const data = await response.json();
      const contractData = data.contract;
      console.log('Contract data loaded:', contractData);
      setContract(contractData);
      
      // Generate the full contract content
      console.log('Looking for template with ID:', contractData.templateId);
      const template = await getTemplateById(contractData.templateId);

      if (template) {
        console.log('Template found:', template.name);

        // Handle custom templates differently
        if (template.category === 'Custom' || template.name.includes('Custom')) {
          console.log('Processing custom template');
          console.log('Contract fields:', contractData.contractFields);
          
          // For custom templates, use the customContractContent from contractFields if available
          let processedContent = '';
          
          if (contractData.contractFields?.customContractContent) {
            console.log('Using custom contract content from contractFields');
            processedContent = contractData.contractFields.customContractContent;
          } else {
            console.log('Using template.template as fallback');
            processedContent = template.template;
          }

          // Replace seller info variables
          processedContent = processedContent.replace(/\{\{sellerInfo\.name\}\}/g, contractData.sellerInfo?.name || '');
          processedContent = processedContent.replace(/\{\{sellerInfo\.email\}\}/g, contractData.sellerInfo?.email || '');
          processedContent = processedContent.replace(/\{\{sellerInfo\.address\}\}/g, contractData.sellerInfo?.address || '');

          // Replace client info variables
          processedContent = processedContent.replace(/\{\{clientInfo\.name\}\}/g, contractData.clientInfo?.name || '');
          processedContent = processedContent.replace(/\{\{clientInfo\.email\}\}/g, contractData.clientInfo?.email || '');
          processedContent = processedContent.replace(/\{\{clientInfo\.address\}\}/g, contractData.clientInfo?.address || '');

          // Replace contract field placeholders with the correct format
          Object.entries(contractData.contractFields || {}).forEach(([key, value]) => {
            if (key !== 'customContractContent') { // Don't replace the custom content itself
              const placeholder = new RegExp(`\\{\\{contractFields\\.${key}\\}\\}`, 'g');
              processedContent = processedContent.replace(placeholder, String(value || ''));
            }
          });

          // Replace contract date
          const currentDate = new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
          processedContent = processedContent.replace(/\{\{contract_date\}\}/g, currentDate);

          // Convert markdown to HTML for custom templates
          const htmlContent = convertMarkdownToHTML(processedContent);
          setContractContent(htmlContent);
        } else {
          // For standard templates, use the normal processing
          const processedContent = processContractTemplate(
            template,
            contractData.sellerInfo,
            contractData.clientInfo,
            contractData.contractFields
          );
          // Convert markdown to HTML
          const htmlContent = convertMarkdownToHTML(processedContent);
          setContractContent(htmlContent);
        }
      } else {
        console.error('Template not found for ID:', contractData.templateId);
        setError(`Template not found: ${contractData.templateId}`);
      }
    } catch (err) {
      console.error('Error in fetchContract:', err);
      setError(err instanceof Error ? err.message : 'Failed to load contract');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignatureComplete = async (signatureData: string) => {
    if (!contract || !termsAccepted) return;

    try {
      setIsSigning(true);
      
      const response = await fetch(`/api/contracts/${contractId}/sign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          signatureData,
          termsAccepted,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save signature');
      }

      const result = await response.json();

      // Update contract status locally
      setContract(prev => prev ? { ...prev, status: 'SIGNED', clientSignature: signatureData, clientSignatureAt: new Date() } : null);

      // Call the callback with the signing response
      onContractSigned(contractId, signatureData, result);

      // Check for invoices and redirect if one exists
      try {
        const invoiceResponse = await fetch(`/api/invoices?contractId=${contractId}`);
        console.log('Invoice response status:', invoiceResponse.status);

        if (invoiceResponse.ok) {
          const invoiceData = await invoiceResponse.json();
          console.log('Invoice data received:', invoiceData);

          if (invoiceData.success && invoiceData.invoices && invoiceData.invoices.length > 0) {
            const invoice = invoiceData.invoices[0]; // Get the first invoice

            // Try checkout URL first, then fallback to download URL
            let invoiceUrl: string | null = null;
            let actionText = '';
            let isPaymentUrl = false;

            console.log('Invoice data:', {
              id: invoice.id,
              checkoutJobId: invoice.checkoutJobId,
              directLink: invoice.directLink,
              downloadUrl: invoice.downloadUrl,
              amount: invoice.amount
            });

            if (invoice.directLink) {
              invoiceUrl = invoice.directLink;
              actionText = 'proceed to payment';
              isPaymentUrl = true;
              console.log('Using DIRECT LINK:', invoiceUrl);
            } else if (invoice.checkoutJobId) {
              invoiceUrl = `https://whop.com/checkout/${invoice.checkoutJobId}`;
              actionText = 'proceed to payment';
              isPaymentUrl = true;
              console.log('Using checkoutJobId URL:', invoiceUrl);
            } else if (invoice.downloadUrl) {
              invoiceUrl = invoice.downloadUrl;
              actionText = 'view your invoice';
              isPaymentUrl = false;
              console.log('Using download URL:', invoiceUrl);
            }

            if (invoiceUrl) {
              // Show toast notification and redirect
              setTimeout(() => {
                // Properly convert Prisma Decimal to number
                const amountValue = convertDecimalToNumber(invoice.amount);

                const redirectMessage = isPaymentUrl
                  ? `You are about to be redirected to the payment page for $${(amountValue * 100).toFixed(2)}`
                  : `You are about to be redirected to your invoice for $${(amountValue * 100).toFixed(2)}`;

                showToast({
                  type: 'success',
                  title: 'Contract Signed Successfully! 🎉',
                  message: redirectMessage,
                  duration: 3000
                });

                // Redirect after a short delay
                setTimeout(() => {
                  window.open(invoiceUrl, '_blank');
                }, 2000);
              }, 1000); // Small delay to let the signing animation complete
            } else {
              console.log('No invoice URL available');
            }
          }
        }
      } catch (invoiceError) {
        console.error('Error checking for invoices:', invoiceError);
      }

      setShowSignature(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save signature');
    } finally {
      setIsSigning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SIGNED':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'COMPLETED':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'DRAFT':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'SIGNED':
        return 'Signed';
      case 'COMPLETED':
        return 'Completed';
      case 'DRAFT':
        return 'Draft';
      default:
        return status;
    }
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f8f9fa',
        fontFamily: 'Inter Display, sans-serif',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto'
          }}></div>
          <p style={{
            marginTop: '16px',
            color: '#6b7280',
            fontFamily: 'Inter Display, sans-serif',
            fontSize: '14px'
          }}>Loading contract...</p>
        </div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (error || !contract) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f8f9fa',
        fontFamily: 'Inter Display, sans-serif',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center', maxWidth: '500px', padding: '20px' }}>
          <AlertCircle style={{
            width: '48px',
            height: '48px',
            color: '#dc2626',
            margin: '0 auto 16px'
          }} />
          <h2 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#111827',
            marginBottom: '8px',
            fontFamily: 'Inter Display, sans-serif'
          }}>Contract Not Found</h2>
          <p style={{
            color: '#6b7280',
            marginBottom: '16px',
            fontFamily: 'Inter Display, sans-serif',
            fontSize: '14px'
          }}>
            {error || 'The requested contract could not be found.'}
          </p>
          {error && error.includes('Template not found') && (
            <div style={{
              backgroundColor: '#fef3c7',
              border: '1px solid #f59e0b',
              borderRadius: '6px',
              padding: '12px',
              marginBottom: '16px',
              textAlign: 'left'
            }}>
              <p style={{
                fontSize: '12px',
                color: '#92400e',
                margin: '0 0 8px 0',
                fontWeight: '600'
              }}>
                Template Issue Detected
              </p>
              <p style={{
                fontSize: '12px',
                color: '#92400e',
                margin: 0,
                lineHeight: '1.4'
              }}>
                The contract exists but its template could not be loaded. This might be due to:
                <br />• Template ID mismatch between contract and available templates
                <br />• Database synchronization issues
                <br />• Missing template in the system
              </p>
            </div>
          )}
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
          <button
            onClick={() => window.history.back()}
              style={{
                padding: '8px 16px',
                backgroundColor: '#6b7280',
                color: 'white',
                borderRadius: '8px',
                border: 'none',
                fontFamily: 'Inter Display, sans-serif',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer'
              }}
              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#4b5563'}
              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#6b7280'}
            >
              Go Back
            </button>
            <button
              onClick={() => window.location.reload()}
            style={{
              padding: '8px 16px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '8px',
              border: 'none',
              fontFamily: 'Inter Display, sans-serif',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#3b82f6'}
          >
              Retry
          </button>
          </div>
        </div>
      </div>
    );
  }


  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: 'var(--muted-background)',
      fontFamily: 'Inter Display, sans-serif',
      padding: '24px'
    }}>
      <div style={{
        maxWidth: '1024px',
        margin: '0 auto'
      }}>
        {/* Contract Document */}
        <div style={{
          backgroundColor: 'var(--card-background)',
          border: '1px solid var(--border-color)',
          borderRadius: '8px',
          boxShadow: '0px 1px 2px var(--shadow-light)',
          padding: '32px',
          marginBottom: '24px'
        }}>
          <div
            style={{
              color: 'var(--text-primary)',
              fontFamily: 'Inter Display, sans-serif',
              fontSize: '16px',
              lineHeight: '1.6'
            }}
            dangerouslySetInnerHTML={{ __html: contractContent }}
          />
        </div>

        {/* Signature Area */}
        {(contract.status === 'DRAFT' || contract.status === 'SENT') && !contract.clientSignature && (
          <div style={{ marginBottom: '24px' }}>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '24px', alignItems: 'center' }}>
              <div style={{
                backgroundColor: '#f8f9fa',
                border: '1px solid rgba(0, 0, 0, 0.121569)',
                borderRadius: '8px',
                boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)',
                padding: '24px',
                width: '100%',
                maxWidth: '1024px',
                position: 'relative'
              }}>
                <div style={{
                  position: 'absolute',
                  top: '12px',
                  left: '16px',
                  fontSize: '11px',
                  color: '#6b7280',
                  fontWeight: '500',
                  fontFamily: 'Inter Display, sans-serif',
                  letterSpacing: '0.025em',
                  textTransform: 'uppercase'
                }}>
                  Signature
                </div>
                <div style={{ marginTop: '20px' }}>
                  <DigitalSignature
                    onSignatureComplete={(data) => setSignatureData(data)}
                    onClear={() => setSignatureData(null)}
                    width={992}
                    height={140}
                  />
                </div>
              </div>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', width: '100%', maxWidth: '1024px', alignItems: 'flex-start' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <Checkbox
                    checked={termsAccepted}
                    onCheckedChange={(checked: boolean) => setTermsAccepted(checked)}
                    size="1"
                  />
                  <Text size="3" as="label" style={{ 
                    cursor: 'pointer', 
                    color: '#374151',
                    fontWeight: '400',
                    lineHeight: '1.5'
                  }}>
                    I agree to the terms and conditions of this contract
                  </Text>
                </div>

                <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
                  <Button
                    onClick={() => {
                      if (signatureData && termsAccepted && !isSigning) {
                        handleSignatureComplete(signatureData);
                      }
                    }}
                    disabled={!termsAccepted || !signatureData || isSigning}
                    size="2"
                    color={termsAccepted && signatureData ? "blue" : "gray"}
                    variant="classic"
                    style={{ 
                      minWidth: '200px',
                      opacity: (!termsAccepted || !signatureData) ? 0.6 : 1,
                      transition: 'all 0.2s ease-in-out'
                    }}
                  >
                    {isSigning ? 'Signing...' : 'Sign Contract'}
                  </Button>
                  
                  <Button
                    onClick={() => onDownloadPDF(contractId)}
                    size="2"
                    variant="ghost"
                    style={{ 
                      display: 'inline-flex', 
                      alignItems: 'center', 
                      gap: '8px',
                      border: '1px solid #d1d5db',
                      color: '#374151',
                      backgroundColor: 'transparent',
                      fontWeight: '500'
                    }}
                  >
                    <Download style={{ width: '16px', height: '16px' }} />
                    Download PDF
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Already Signed Message */}
        {contract.clientSignature && (
          <div style={{
            backgroundColor: '#f0f9ff',
            border: '1px solid #0ea5e9',
            borderRadius: '8px',
            padding: '24px',
            marginBottom: '24px',
            textAlign: 'center'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '12px',
              marginBottom: '8px'
            }}>
              <CheckCircle style={{ width: '24px', height: '24px', color: '#0ea5e9' }} />
              <Text size="5" weight="bold" style={{ color: '#0c4a6e' }}>
                Contract Signed
              </Text>
            </div>
            <Text size="4" style={{ color: '#0c4a6e' }}>
              This contract was signed on {contract.clientSignatureAt ? formatDate(contract.clientSignatureAt) : 'Unknown date'}.
            </Text>
          </div>
        )}

        {/* Download Button for Signed Contracts */}
        {contract.clientSignature && (
        <div style={{
          backgroundColor: 'white',
          border: '1px solid rgba(0, 0, 0, 0.121569)',
          borderRadius: '8px',
          boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)',
            padding: '16px',
          textAlign: 'center'
        }}>
            <Button
            onClick={() => onDownloadPDF(contractId)}
              size="2"
              variant="ghost"
            style={{
              display: 'inline-flex',
              alignItems: 'center',
                gap: '8px',
                border: '1px solid #d1d5db',
                color: '#374151',
                backgroundColor: 'transparent'
              }}
            >
              <Download style={{ width: '16px', height: '16px' }} />
              Download PDF
            </Button>
        </div>
        )}
      </div>
    </div>
  );
}
