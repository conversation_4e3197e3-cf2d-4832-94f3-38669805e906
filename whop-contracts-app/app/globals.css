@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://api.fontshare.com/v2/css?f[]=acid-grotesk@500&display=swap');

@layer theme, base, frosted_ui, components, utilities;

@import "tailwindcss/theme.css" layer(theme);
@import "tailwindcss/preflight.css" layer(base);
@import "tailwindcss/utilities.css" layer(utilities);
@import "@whop/react/styles.css" layer(frosted_ui);
@import "frosted-ui/styles.css" layer(frosted_ui);
@import "../styles/create-contract.css" layer(components);
@import "../styles/invoice-interface.css" layer(components);

@config '../tailwind.config.ts';

/* Prevent flash of incorrect theme */
html {
	color-scheme: light dark;
}

html.dark {
	color-scheme: dark;
}

html.light {
	color-scheme: light;
}

/* CSS Variables for Dark Mode */
:root {
	/* Light mode colors */
	--background: #ffffff;
	--foreground: #000000;
	--muted-background: #f8f9fa;
	--card-background: #ffffff;
	--border-color: #e5e7eb;
	--input-background: rgba(255, 255, 255, 0.9);
	--input-border: rgba(0, 0, 0, 0.121569);
	--text-primary: #202020;
	--text-secondary: #6b7280;
	--text-muted: #9ca3af;
	--hover-background: #f3f4f6;
	--dropdown-background: #ffffff;
	--shadow-light: rgba(0, 0, 0, 0.06);
	--shadow-medium: rgba(0, 0, 0, 0.1);
	--focus-ring: rgba(59, 130, 246, 0.1);
	--separator: #e5e7eb;
	--success-color: #10b981;
	--error-color: #ef4444;
	--warning-color: #f59e0b;
}

/* Dark mode styles with higher specificity */
html.dark,
html[data-theme="dark"],
[data-theme="dark"],
html.dark *,
html[data-theme="dark"] *,
[data-theme="dark"] * {
	/* Dark mode colors - Very dark theme */
	--background: #0a0a0a !important;
	--foreground: #ffffff !important;
	--muted-background: #111111 !important;
	--card-background: #1a1a1a !important;
	--border-color: #333333 !important;
	--input-background: rgba(26, 26, 26, 0.95) !important;
	--input-border: rgba(255, 255, 255, 0.2) !important;
	--text-primary: #ffffff !important;
	--text-secondary: #b3b3b3 !important;
	--text-muted: #808080 !important;
	--hover-background: #2a2a2a !important;
	--dropdown-background: #1a1a1a !important;
	--shadow-light: rgba(0, 0, 0, 0.4) !important;
	--shadow-medium: rgba(0, 0, 0, 0.6) !important;
	--focus-ring: rgba(59, 130, 246, 0.4) !important;
	--separator: #333333 !important;
	--success-color: #10b981 !important;
	--error-color: #ef4444 !important;
	--warning-color: #f59e0b !important;
}

body {
	background: var(--background);
	color: var(--foreground);
	font-family: var(--font-inter), Inter, system-ui, sans-serif;
	transition: background-color 0.2s ease, color 0.2s ease;
}

/* Force dark mode body background */
html.dark body,
html[data-theme="dark"] body,
[data-theme="dark"] body {
	background: #0a0a0a !important;
	color: #ffffff !important;
}

/* Ensure theme transitions are smooth */
* {
	transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Force dark mode styles */
html.dark *,
html[data-theme="dark"] *,
[data-theme="dark"] * {
	border-color: var(--border-color) !important;
}

html.dark body,
html[data-theme="dark"] body,
[data-theme="dark"] body {
	background: var(--background) !important;
	color: var(--foreground) !important;
}

/* Ensure all containers use theme colors */
html.dark .create-contract-container,
html[data-theme="dark"] .create-contract-container,
[data-theme="dark"] .create-contract-container {
	background: var(--muted-background) !important;
}

html.dark .form-panel,
html.dark .preview-panel,
html[data-theme="dark"] .form-panel,
html[data-theme="dark"] .preview-panel,
[data-theme="dark"] .form-panel,
[data-theme="dark"] .preview-panel {
	background: var(--card-background) !important;
	border: 1px solid var(--border-color) !important;
}

/* Force dark mode on all contract builder elements */
html.dark,
html[data-theme="dark"],
[data-theme="dark"] {
	/* Force main container backgrounds */
	div[style*="minHeight: '100vh'"],
	div[style*="min-height: 100vh"],
	div[style*="backgroundColor: 'var(--muted-background)'"],
	div[style*="background: var(--muted-background)"] {
		background: #111111 !important;
		color: #ffffff !important;
	}

	/* Override any inline styles that might conflict */
	div[style*="backgroundColor: 'white'"],
	div[style*="background: white"],
	div[style*="backgroundColor: white"],
	div[style*="backgroundColor: '#ffffff'"],
	div[style*="background: #ffffff"] {
		background: #1a1a1a !important;
		color: #ffffff !important;
	}

	div[style*="backgroundColor: '#f8f9fa'"],
	div[style*="background: #f8f9fa"],
	div[style*="backgroundColor: 'var(--card-background)'"],
	div[style*="background: var(--card-background)"] {
		background: #1a1a1a !important;
		color: #ffffff !important;
	}

	/* Force all divs to use dark backgrounds */
	div {
		background-color: inherit !important;
		color: #ffffff !important;
	}

	/* Specific overrides for main containers */
	div[style*="display: 'flex'"][style*="height: 'calc(100vh - 73px)"] {
		background: #111111 !important;
	}

	input, textarea, select {
		background: rgba(26, 26, 26, 0.95) !important;
		color: #ffffff !important;
		border-color: #333333 !important;
	}

	/* Force text colors */
	* {
		color: #ffffff !important;
	}

	/* Override specific text colors */
	[style*="color: #"] {
		color: #ffffff !important;
	}

	/* Dashboard specific overrides */
	.contracts-dashboard,
	[class*="dashboard"] {
		background: #0a0a0a !important;
		color: #ffffff !important;
	}

	/* Table and card overrides - make tables blend with background */
	table, .table, [class*="Table"] {
		background: transparent !important;
		color: #ffffff !important;
	}

	/* Table rows and cells */
	tr, td, th, [class*="Row"], [class*="Cell"], [class*="Header"] {
		background: transparent !important;
		color: #ffffff !important;
		border-color: #333333 !important;
	}

	/* Button overrides for better visibility */
	button:not([class*="blue"]):not([class*="primary"]) {
		background: #1a1a1a !important;
		color: #ffffff !important;
		border-color: #333333 !important;
	}

	/* Remove blue borders from all buttons in dark mode */
	button, [role="button"] {
		border-color: #333333 !important;
		box-shadow: none !important;
	}

	/* Specific overrides for blue buttons to remove blue borders */
	button[color="blue"],
	[class*="blue"] button,
	button[class*="blue"] {
		border: 1px solid #333333 !important;
		box-shadow: none !important;
	}

	/* Override frosted-ui button styles */
	[data-accent-color="blue"] {
		border-color: #333333 !important;
		box-shadow: none !important;
	}
}

/* Disable text wrapping globally */
* {
	white-space: nowrap;
}

/* Re-enable text wrapping for specific elements that need it */
p, div, span, textarea, input[type="text"], input[type="email"], input[type="password"] {
	white-space: normal;
}

/* Allow text wrapping for specific content areas */
.receipt-total-label, .frequent-button .button-text, .quick-add-button .button-text {
	white-space: normal !important;
}

/* Ensure proper text wrapping for content areas */
.content, .description, .text-content, .message, .notification {
	white-space: normal;
}

/* Force no text wrapping in contract builder */
.create-contract-container * {
	white-space: nowrap !important;
}

/* Allow text wrapping only for input fields and textareas in contract builder */
.create-contract-container input,
.create-contract-container textarea {
	white-space: normal !important;
}

/* Ensure all button text, labels, and UI text don't wrap */
button, .button-text, .breadcrumb-text, .label, .label-text, .radio-text, .price-type-text, .placeholder, .text, .preview-footer-text {
	white-space: nowrap !important;
}

/* Override any Tailwind text wrapping classes */
.text-center, .text-left, .text-right, .text-justify, .whitespace-normal, .whitespace-pre, .whitespace-pre-line, .whitespace-pre-wrap {
	white-space: nowrap !important;
}

/* Remove grey border from WhopApp component */
[data-whop-app] {
	border: none !important;
	outline: none !important;
	box-shadow: none !important;
}

/* Remove any borders from the main container */
body > div {
	border: none !important;
	outline: none !important;
}

/* Additional dark mode enforcement */
html.dark,
html[data-theme="dark"],
[data-theme="dark"] {
	/* Ensure the root element has dark background */
	background: #0a0a0a !important;
}

html.dark *,
html[data-theme="dark"] *,
[data-theme="dark"] * {
	/* Force all elements to inherit proper colors */
	background-color: inherit;
	color: inherit;
}

/* Specific overrides for contract builder */
html.dark [style*="minHeight: '100vh'"],
html[data-theme="dark"] [style*="minHeight: '100vh'"],
[data-theme="dark"] [style*="minHeight: '100vh'"] {
	background: #111111 !important;
}

html.dark [style*="width: '50%'"],
html[data-theme="dark"] [style*="width: '50%'"],
[data-theme="dark"] [style*="width: '50%'"] {
	background: #1a1a1a !important;
	color: #ffffff !important;
}

/* Additional table styling for dark mode */
html.dark [class*="Table"],
html[data-theme="dark"] [class*="Table"],
[data-theme="dark"] [class*="Table"] {
	background: transparent !important;
	border: none !important;
}

html.dark [style*="borderBottom"],
html[data-theme="dark"] [style*="borderBottom"],
[data-theme="dark"] [style*="borderBottom"] {
	border-bottom: none !important;
}

/* Remove all table borders in dark mode */
html.dark table *,
html[data-theme="dark"] table *,
[data-theme="dark"] table * {
	border: none !important;
	background: transparent !important;
}
