/* Clean Frost Dark Mode - Only affects dark mode, preserves light mode exactly as is */

/* Frost Dark Mode Variables - Only applied in dark mode */
[data-theme="dark"],
html.dark {
  /* Clean dark backgrounds matching the image */
  --frost-bg-primary: #0f0f0f;
  --frost-bg-secondary: #1a1a1a;
  --frost-surface: rgba(26, 26, 26, 0.8);
  --frost-surface-hover: rgba(42, 42, 42, 0.9);
  --frost-glass: rgba(26, 26, 26, 0.7);
  --frost-glass-hover: rgba(42, 42, 42, 0.8);

  /* Clean text colors */
  --frost-text-primary: #ffffff;
  --frost-text-secondary: rgba(255, 255, 255, 0.7);
  --frost-text-muted: rgba(255, 255, 255, 0.5);
  --frost-text-placeholder: rgba(255, 255, 255, 0.4);

  /* Clean borders */
  --frost-border: rgba(255, 255, 255, 0.1);
  --frost-border-hover: rgba(255, 255, 255, 0.2);
  --frost-border-focus: #3b82f6;

  /* Shadows for depth */
  --frost-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  --frost-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.4);
}

/* Apply frost theme to body */
body {
  background: var(--background-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Frost glass effect base */
.frost-glass {
  background: var(--surface-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass);
}

/* Ensure all elements inherit the theme with smooth transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, backdrop-filter 0.3s ease;
}

/* ===== FROST DARK MODE UI COMPONENTS ===== */

/* Dropdowns */
[data-theme="dark"] .dropdown-menu,
html.dark .dropdown-menu,
[data-theme="dark"] [role="menu"],
html.dark [role="menu"],
[data-theme="dark"] .card-dropdown,
html.dark .card-dropdown,
[data-theme="dark"] .currency-selector,
html.dark .currency-selector {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
  box-shadow: var(--shadow-glass) !important;
  color: var(--text-primary) !important;
}

/* Filter dropdown buttons should use frost-ui styling like light mode */
[data-theme="dark"] button[data-variant="surface"],
html.dark button[data-variant="surface"] {
  /* Let frosted-ui handle surface variant naturally - don't override */
  background: unset !important;
  border: unset !important;
  color: unset !important;
  backdrop-filter: unset !important;
  -webkit-backdrop-filter: unset !important;
  box-shadow: unset !important;
}

/* Dropdown content should use frost-ui styling */
[data-theme="dark"] [data-radix-popper-content-wrapper],
html.dark [data-radix-popper-content-wrapper],
[data-theme="dark"] [role="menu"][data-radix-popper-content-wrapper],
html.dark [role="menu"][data-radix-popper-content-wrapper] {
  /* Let frosted-ui handle dropdown content naturally */
  background: unset !important;
  border: unset !important;
  backdrop-filter: unset !important;
  -webkit-backdrop-filter: unset !important;
  box-shadow: unset !important;
}

[data-theme="dark"] .dropdown-menu:hover,
html.dark .dropdown-menu:hover,
[data-theme="dark"] .card-dropdown:hover,
html.dark .card-dropdown:hover,
[data-theme="dark"] .currency-selector:hover,
html.dark .currency-selector:hover {
  background: var(--surface-glass-hover) !important;
  border-color: var(--border-primary) !important;
}

/* Input Fields */
[data-theme="dark"] input,
html.dark input,
[data-theme="dark"] textarea,
html.dark textarea,
[data-theme="dark"] .input-field,
html.dark .input-field,
[data-theme="dark"] .input-container,
html.dark .input-container,
[data-theme="dark"] .price-input,
html.dark .price-input,
[data-theme="dark"] .description-textarea,
html.dark .description-textarea {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
  color: var(--text-primary) !important;
  box-shadow: var(--shadow-sm) !important;
}

[data-theme="dark"] input::placeholder,
html.dark input::placeholder,
[data-theme="dark"] textarea::placeholder,
html.dark textarea::placeholder,
[data-theme="dark"] .placeholder,
html.dark .placeholder {
  color: var(--text-placeholder) !important;
}

[data-theme="dark"] input:focus,
html.dark input:focus,
[data-theme="dark"] textarea:focus,
html.dark textarea:focus,
[data-theme="dark"] .input-field:focus-within,
html.dark .input-field:focus-within,
[data-theme="dark"] .input-container:focus-within,
html.dark .input-container:focus-within {
  border-color: var(--border-focus) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
  background: var(--surface-glass-hover) !important;
}

/* Cards */
[data-theme="dark"] .card,
html.dark .card,
[data-theme="dark"] .form-panel,
html.dark .form-panel,
[data-theme="dark"] .preview-panel,
html.dark .preview-panel,
[data-theme="dark"] .contract-card,
html.dark .contract-card,
[data-theme="dark"] .receipt-card,
html.dark .receipt-card,
[data-theme="dark"] .invoice-preview-card,
html.dark .invoice-preview-card,
[data-theme="dark"] .receipt-preview-card,
html.dark .receipt-preview-card,
[data-theme="dark"] .contract-preview-document,
html.dark .contract-preview-document,
[data-theme="dark"] .email-preview-document,
html.dark .email-preview-document {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
  box-shadow: var(--shadow-glass) !important;
  color: var(--text-primary) !important;
}

/* Card Headers */
[data-theme="dark"] .contract-header,
html.dark .contract-header,
[data-theme="dark"] .receipt-header,
html.dark .receipt-header,
[data-theme="dark"] .contract-preview-header,
html.dark .contract-preview-header,
[data-theme="dark"] .email-preview-header,
html.dark .email-preview-header,
[data-theme="dark"] .invoice-preview-header,
html.dark .invoice-preview-header {
  background: var(--surface-secondary) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-glass) !important;
  color: var(--text-primary) !important;
}

/* Buttons - but exclude buttons with specific styling */
[data-theme="dark"] button:not([data-accent-color]):not([data-variant="soft"]):not([style*="background: linear-gradient"]),
html.dark button:not([data-accent-color]):not([data-variant="soft"]):not([style*="background: linear-gradient"]),
[data-theme="dark"] .button:not([data-accent-color]):not([data-variant="soft"]):not([style*="background: linear-gradient"]),
html.dark .button:not([data-accent-color]):not([data-variant="soft"]):not([style*="background: linear-gradient"]),
[data-theme="dark"] .download-button,
html.dark .download-button,
[data-theme="dark"] .frequent-button,
html.dark .frequent-button,
[data-theme="dark"] .quick-add-button,
html.dark .quick-add-button,
[data-theme="dark"] .quick-add-btn,
html.dark .quick-add-btn,
[data-theme="dark"] .segmented-button,
html.dark .segmented-button,
[data-theme="dark"] .price-toggle,
html.dark .price-toggle {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
  color: var(--text-primary) !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Preserve light mode colors for status buttons and colored buttons */
[data-theme="dark"] button[data-accent-color="green"],
html.dark button[data-accent-color="green"],
[data-theme="dark"] button[data-accent-color="blue"],
html.dark button[data-accent-color="blue"],
[data-theme="dark"] button[data-accent-color="red"],
html.dark button[data-accent-color="red"],
[data-theme="dark"] button[data-accent-color="gray"],
html.dark button[data-accent-color="gray"],
[data-theme="dark"] button[data-variant="soft"],
html.dark button[data-variant="soft"] {
  /* Let frosted-ui handle the colors naturally - don't override */
  background: unset !important;
  border: unset !important;
  color: unset !important;
  backdrop-filter: unset !important;
  -webkit-backdrop-filter: unset !important;
  box-shadow: unset !important;
}

/* Ensure Create Contract button stays blue */
[data-theme="dark"] button[data-accent-color="blue"][data-variant="classic"],
html.dark button[data-accent-color="blue"][data-variant="classic"] {
  /* Let frosted-ui handle blue classic variant naturally */
  background: unset !important;
  border: unset !important;
  color: unset !important;
  backdrop-filter: unset !important;
  -webkit-backdrop-filter: unset !important;
  box-shadow: unset !important;
}

/* Status buttons in tables should preserve their light mode colors */
[data-theme="dark"] table button[data-variant="soft"],
html.dark table button[data-variant="soft"],
[data-theme="dark"] .table button[data-variant="soft"],
html.dark .table button[data-variant="soft"] {
  /* Let frosted-ui handle soft variant colors naturally */
  background: unset !important;
  border: unset !important;
  color: unset !important;
  backdrop-filter: unset !important;
  -webkit-backdrop-filter: unset !important;
  box-shadow: unset !important;
}

[data-theme="dark"] button:hover:not([data-accent-color]):not([data-variant="soft"]):not([style*="background: linear-gradient"]),
html.dark button:hover:not([data-accent-color]):not([data-variant="soft"]):not([style*="background: linear-gradient"]),
[data-theme="dark"] .button:hover:not([data-accent-color]):not([data-variant="soft"]):not([style*="background: linear-gradient"]),
html.dark .button:hover:not([data-accent-color]):not([data-variant="soft"]):not([style*="background: linear-gradient"]),
[data-theme="dark"] .download-button:hover,
html.dark .download-button:hover,
[data-theme="dark"] .frequent-button:hover,
html.dark .frequent-button:hover,
[data-theme="dark"] .quick-add-button:hover,
html.dark .quick-add-button:hover,
[data-theme="dark"] .quick-add-btn:hover,
html.dark .quick-add-btn:hover {
  background: var(--surface-glass-hover) !important;
  border-color: var(--border-primary) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md) !important;
}

/* Don't override hover states for colored buttons */
[data-theme="dark"] button[data-accent-color="green"]:hover,
html.dark button[data-accent-color="green"]:hover,
[data-theme="dark"] button[data-accent-color="blue"]:hover,
html.dark button[data-accent-color="blue"]:hover,
[data-theme="dark"] button[data-accent-color="red"]:hover,
html.dark button[data-accent-color="red"]:hover,
[data-theme="dark"] button[data-accent-color="gray"]:hover,
html.dark button[data-accent-color="gray"]:hover,
[data-theme="dark"] button[data-variant="surface"]:hover,
html.dark button[data-variant="surface"]:hover,
[data-theme="dark"] button[data-variant="soft"]:hover,
html.dark button[data-variant="soft"]:hover,
[data-theme="dark"] button[style*="background: linear-gradient"]:hover,
html.dark button[style*="background: linear-gradient"]:hover {
  background: unset !important;
  border-color: unset !important;
  transform: unset !important;
  box-shadow: unset !important;
}

/* Primary/Accent Buttons */
[data-theme="dark"] .primary-button,
html.dark .primary-button,
[data-theme="dark"] .pay-now-button,
html.dark .pay-now-button,
[data-theme="dark"] .send-button,
html.dark .send-button,
[data-theme="dark"] .email-cta-button,
html.dark .email-cta-button,
[data-theme="dark"] .preview-action-button:not(.secondary),
html.dark .preview-action-button:not(.secondary) {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-hover) 100%) !important;
  color: var(--text-on-accent) !important;
  border: 1px solid var(--accent-primary) !important;
  box-shadow: var(--accent-shadow) !important;
}

[data-theme="dark"] .primary-button:hover,
html.dark .primary-button:hover,
[data-theme="dark"] .pay-now-button:hover,
html.dark .pay-now-button:hover,
[data-theme="dark"] .send-button:hover,
html.dark .send-button:hover,
[data-theme="dark"] .email-cta-button:hover,
html.dark .email-cta-button:hover {
  background: linear-gradient(135deg, var(--accent-hover) 0%, #1d4ed8 100%) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
  transform: translateY(-2px);
}

/* Secondary Buttons */
[data-theme="dark"] .preview-action-button.secondary,
html.dark .preview-action-button.secondary,
[data-theme="dark"] .more-button,
html.dark .more-button,
[data-theme="dark"] .more-btn,
html.dark .more-btn {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .preview-action-button.secondary:hover,
html.dark .preview-action-button.secondary:hover,
[data-theme="dark"] .more-button:hover,
html.dark .more-button:hover,
[data-theme="dark"] .more-btn:hover,
html.dark .more-btn:hover {
  background: var(--surface-glass-hover) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-primary) !important;
}

/* Segmented Controls */
[data-theme="dark"] .segmented-control,
html.dark .segmented-control,
[data-theme="dark"] .price-toggle-group,
html.dark .price-toggle-group,
[data-theme="dark"] .preview-tabs,
html.dark .preview-tabs {
  background: var(--surface-secondary) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
}

[data-theme="dark"] .segmented-button.active,
html.dark .segmented-button.active,
[data-theme="dark"] .price-toggle.selected,
html.dark .price-toggle.selected,
[data-theme="dark"] .preview-tab.active,
html.dark .preview-tab.active,
[data-theme="dark"] .preview-tab:not(.inactive),
html.dark .preview-tab:not(.inactive) {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  color: var(--text-primary) !important;
  border: 1px solid var(--border-glass) !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Radio Buttons */
[data-theme="dark"] .radio,
html.dark .radio,
[data-theme="dark"] .radio-button,
html.dark .radio-button {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 2px solid var(--border-glass) !important;
}

[data-theme="dark"] .radio:not(.unselected),
html.dark .radio:not(.unselected),
[data-theme="dark"] .radio-item.selected .radio-button,
html.dark .radio-item.selected .radio-button {
  background: var(--accent-primary) !important;
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

[data-theme="dark"] .radio-text,
html.dark .radio-text,
[data-theme="dark"] .radio-label,
html.dark .radio-label {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .radio-item.selected .radio-label,
html.dark .radio-item.selected .radio-label {
  color: var(--text-primary) !important;
}

/* Tables */
[data-theme="dark"] table,
html.dark table,
[data-theme="dark"] .table,
html.dark .table {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
}

[data-theme="dark"] th,
html.dark th,
[data-theme="dark"] td,
html.dark td {
  border-color: var(--border-glass) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] tr:hover,
html.dark tr:hover {
  background: var(--surface-hover) !important;
}

/* Modals and Overlays */
[data-theme="dark"] .modal,
html.dark .modal,
[data-theme="dark"] .overlay,
html.dark .overlay,
[data-theme="dark"] .dialog,
html.dark .dialog {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
  box-shadow: var(--shadow-glass) !important;
}

[data-theme="dark"] .modal-backdrop,
html.dark .modal-backdrop,
[data-theme="dark"] .overlay-backdrop,
html.dark .overlay-backdrop {
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Notifications and Toasts */
[data-theme="dark"] .toast,
html.dark .toast,
[data-theme="dark"] .notification,
html.dark .notification {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
  box-shadow: var(--shadow-glass) !important;
  color: var(--text-primary) !important;
}

/* Badges */
[data-theme="dark"] .badge,
html.dark .badge {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
  color: var(--text-primary) !important;
}

/* Dividers */
[data-theme="dark"] .divider,
html.dark .divider,
[data-theme="dark"] .section-divider,
html.dark .section-divider,
[data-theme="dark"] hr,
html.dark hr {
  border-color: var(--border-glass) !important;
  background: var(--border-glass) !important;
}

/* Text Colors */
[data-theme="dark"] .text-primary,
html.dark .text-primary,
[data-theme="dark"] .label,
html.dark .label,
[data-theme="dark"] .label-text,
html.dark .label-text,
[data-theme="dark"] .section-label,
html.dark .section-label,
[data-theme="dark"] .contract-title-text,
html.dark .contract-title-text,
[data-theme="dark"] .receipt-title,
html.dark .receipt-title,
[data-theme="dark"] .contract-preview-title,
html.dark .contract-preview-title,
[data-theme="dark"] .email-preview-title,
html.dark .email-preview-title {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .text-secondary,
html.dark .text-secondary,
[data-theme="dark"] .breadcrumb-text,
html.dark .breadcrumb-text,
[data-theme="dark"] .input-email,
html.dark .input-email,
[data-theme="dark"] .contract-label,
html.dark .contract-label,
[data-theme="dark"] .receipt-date,
html.dark .receipt-date,
[data-theme="dark"] .preview-label,
html.dark .preview-label {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .text-muted,
html.dark .text-muted,
[data-theme="dark"] .text,
html.dark .text,
[data-theme="dark"] .preview-footer-text,
html.dark .preview-footer-text,
[data-theme="dark"] .invoice-preview-number,
html.dark .invoice-preview-number {
  color: var(--text-muted) !important;
}

/* ===== SPECIFIC COMPONENT STYLING ===== */

/* Dashboard and Container Backgrounds */
[data-theme="dark"] .create-contract-container,
html.dark .create-contract-container,
[data-theme="dark"] .invoice-interface-container,
html.dark .invoice-interface-container {
  background: var(--background-primary) !important;
}

[data-theme="dark"] .create-contract-background,
html.dark .create-contract-background {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%) !important;
  opacity: 0.3 !important;
}

[data-theme="dark"] .two-panel-layout,
html.dark .two-panel-layout,
[data-theme="dark"] .main-content,
html.dark .main-content {
  background: transparent !important;
}

[data-theme="dark"] .left-panel,
html.dark .left-panel,
[data-theme="dark"] .right-panel,
html.dark .right-panel {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
}

/* Headers */
[data-theme="dark"] .create-contract-header,
html.dark .create-contract-header,
[data-theme="dark"] .invoice-header,
html.dark .invoice-header {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-glass) !important;
}

/* Form Sections */
[data-theme="dark"] .form-section,
html.dark .form-section,
[data-theme="dark"] .customer-section,
html.dark .customer-section,
[data-theme="dark"] .product-section,
html.dark .product-section,
[data-theme="dark"] .payment-collection-section,
html.dark .payment-collection-section,
[data-theme="dark"] .price-type-section,
html.dark .price-type-section,
[data-theme="dark"] .advanced-options-section,
html.dark .advanced-options-section,
[data-theme="dark"] .payment-options-section,
html.dark .payment-options-section,
[data-theme="dark"] .description-section,
html.dark .description-section {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
}

[data-theme="dark"] .form-section.expanded,
html.dark .form-section.expanded,
[data-theme="dark"] .price-type-section.expanded,
html.dark .price-type-section.expanded {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--accent-primary) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

/* Section Headers */
[data-theme="dark"] .payment-collection-header,
html.dark .payment-collection-header,
[data-theme="dark"] .price-type-header,
html.dark .price-type-header,
[data-theme="dark"] .advanced-options-header,
html.dark .advanced-options-header,
[data-theme="dark"] .payment-options-header,
html.dark .payment-options-header {
  background: var(--surface-secondary) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-glass) !important;
}

[data-theme="dark"] .payment-collection-header:hover,
html.dark .payment-collection-header:hover,
[data-theme="dark"] .price-type-header:hover,
html.dark .price-type-header:hover,
[data-theme="dark"] .advanced-options-header:hover,
html.dark .advanced-options-header:hover,
[data-theme="dark"] .payment-options-header:hover,
html.dark .payment-options-header:hover {
  background: var(--surface-hover) !important;
}

/* Contract Preview Content */
[data-theme="dark"] .contract-preview-content,
html.dark .contract-preview-content,
[data-theme="dark"] .email-preview-content,
html.dark .email-preview-content,
[data-theme="dark"] .contract-document-content,
html.dark .contract-document-content,
[data-theme="dark"] .email-body,
html.dark .email-body {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  color: var(--text-primary) !important;
}

[data-theme="dark"] .contract-document-content h3,
html.dark .contract-document-content h3 {
  color: var(--text-primary) !important;
  border-bottom-color: var(--border-glass) !important;
}

/* Email Meta */
[data-theme="dark"] .email-meta,
html.dark .email-meta {
  background: var(--surface-secondary) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
}

/* Icons and Visual Elements */
[data-theme="dark"] .invoice-preview-icon,
html.dark .invoice-preview-icon,
[data-theme="dark"] .plus,
html.dark .plus {
  background: var(--accent-primary) !important;
  color: var(--text-on-accent) !important;
}

[data-theme="dark"] .visa-logo,
html.dark .visa-logo {
  background: var(--surface-glass) !important;
  border: 1px solid var(--border-glass) !important;
}

/* Chevrons and Icons */
[data-theme="dark"] .chevron-down,
html.dark .chevron-down,
[data-theme="dark"] .chevron-up,
html.dark .chevron-up,
[data-theme="dark"] .chevron-right,
html.dark .chevron-right,
[data-theme="dark"] .dropdown-arrow,
html.dark .dropdown-arrow,
[data-theme="dark"] .back-icon,
html.dark .back-icon {
  color: var(--text-secondary) !important;
}

/* Clear and Close Buttons */
[data-theme="dark"] .clear-button,
html.dark .clear-button,
[data-theme="dark"] .close-icon,
html.dark .close-icon,
[data-theme="dark"] .back-button,
html.dark .back-button {
  background: var(--surface-glass) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass) !important;
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .clear-button:hover,
html.dark .clear-button:hover,
[data-theme="dark"] .close-icon:hover,
html.dark .close-icon:hover,
[data-theme="dark"] .back-button:hover,
html.dark .back-button:hover {
  background: var(--surface-hover) !important;
  color: var(--text-primary) !important;
}

/* Ensure proper contrast for all text elements */
[data-theme="dark"] *,
html.dark * {
  scrollbar-width: thin;
  scrollbar-color: var(--border-glass) transparent;
}

[data-theme="dark"] *::-webkit-scrollbar,
html.dark *::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-theme="dark"] *::-webkit-scrollbar-track,
html.dark *::-webkit-scrollbar-track {
  background: transparent;
}

[data-theme="dark"] *::-webkit-scrollbar-thumb,
html.dark *::-webkit-scrollbar-thumb {
  background: var(--border-glass);
  border-radius: 4px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

[data-theme="dark"] *::-webkit-scrollbar-thumb:hover,
html.dark *::-webkit-scrollbar-thumb:hover {
  background: var(--border-primary);
}
