/* Invoice Interface Styles */
.invoice-interface-container {
  min-height: 100vh;
  background: var(--card-background);
  font-family: 'Inter Display', sans-serif;
  display: flex;
  flex-direction: column;
}

/* Header */
.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: var(--card-background);
  border-bottom: 1px solid var(--border-color);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background: var(--hover-background);
}

.back-icon {
  width: 16px;
  height: 16px;
  color: var(--text-secondary);
}

.breadcrumb-text {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.send-button {
  background: #1754D8 !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
}

.send-button:hover {
  background: #1441b8 !important;
}

.send-button:disabled {
  background: #ccc !important;
  cursor: not-allowed !important;
}

/* Two Panel Layout */
.two-panel-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
  height: calc(100vh - 80px);
  max-width: none;
  margin: 0;
}

/* Left Panel */
.left-panel {
  background: white;
  border-radius: 0;
  padding: 24px;
  box-shadow: none;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
  height: 100%;
}

.form-sections {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.form-section {
  padding: 24px 0;
}

.form-section:first-child {
  padding-top: 0;
}

.form-section:last-child {
  padding-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header.expandable {
  cursor: pointer;
}

.section-label {
  font-size: 14px;
  font-weight: 500;
  color: #202020;
  margin: 0;
}

.chevron-down,
.chevron-up,
.chevron-right {
  color: #666;
  font-size: 12px;
  transition: transform 0.2s;
}

.section-header.expandable:hover .chevron-down,
.section-header.expandable:hover .chevron-up,
.section-header.expandable:hover .chevron-right {
  transform: scale(1.1);
}

/* Input Fields */
.input-field-container {
  margin-bottom: 16px;
}

.input-field {
  background: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.2s;
}

.input-field:hover {
  border-color: #d0d0d0;
}

.input-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.input-name {
  font-size: 14px;
  font-weight: 500;
  color: #202020;
  margin: 0;
}

.input-email {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.486275);
  margin: 0;
}

.input-actions {
  display: flex;
  align-items: center;
}

.clear-button {
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  color: #666;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s;
}

.clear-button:hover {
  background: #f0f0f0;
  color: #333;
}

/* Section Dividers */
.section-divider {
  height: 1px;
  background: #e8e8e8;
  margin: 24px 0;
}

/* Radio Groups */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.radio-item.selected .radio-label {
  color: rgba(0, 0, 0, 0.875);
}

.radio-button {
  width: 16px;
  height: 16px;
  border: 1px solid rgba(0, 0, 0, 0.192157);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.radio-item.selected .radio-button {
  background: #1754D8;
  border-color: #1754D8;
}

.radio-circle {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: transparent;
  transition: all 0.2s;
}

.radio-item.selected .radio-circle {
  background: white;
}

.radio-label {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  margin: 0;
}

/* Card Info Section */
.card-info-section {
  margin-bottom: 16px;
}

.card-label {
  font-size: 14px;
  font-weight: 500;
  color: #202020;
  margin: 0 0 8px 0;
}

.card-dropdown {
  background: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.card-dropdown:hover {
  border-color: #d0d0d0;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.visa-logo {
  width: 24px;
  height: 18px;
  background: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  position: relative;
}

.visa-logo::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16.96px;
  height: 5.49px;
  background: linear-gradient(45deg, #265CCF 0%, #FFB301 100%);
  border-radius: 2px;
}

.card-text {
  flex: 1;
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.87451);
  margin: 0;
}

.dropdown-arrow {
  color: #838383;
  font-size: 12px;
}

/* Textarea */
.textarea-container {
  margin-bottom: 16px;
}

.description-textarea {
  width: 100%;
  min-height: 72px;
  padding: 12px;
  border: 1px solid rgba(0, 0, 0, 0.121569);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  font-family: 'Inter Display', sans-serif;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.01em;
  color: rgba(0, 0, 0, 0.87451);
  resize: none;
  outline: none;
  transition: border-color 0.2s;
}

.description-textarea:focus {
  border-color: #1754D8;
  box-shadow: 0 0 0 2px rgba(23, 84, 216, 0.1);
}

/* Price Section */
.form-section.expanded {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin: 16px 0;
}

.price-toggle-group {
  display: flex;
  gap: 0;
  margin-bottom: 24px;
  border: 1px solid rgba(0, 0, 0, 0.121569);
  border-radius: 8px;
  overflow: hidden;
}

.price-toggle {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: white;
  border-right: 1px solid rgba(0, 0, 0, 0.121569);
  font-size: 12px;
  font-weight: 500;
  color: #202020;
  cursor: pointer;
  transition: all 0.2s;
}

.price-toggle:last-child {
  border-right: none;
}

.price-toggle.selected {
  background: #EBF2FF;
  border-color: #7EA7F5;
  color: #265CCF;
}

.price-toggle:hover:not(.selected) {
  background: #f5f5f5;
}

.price-input-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.price-input-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.price-label {
  font-size: 14px;
  font-weight: 500;
  color: #202020;
  margin: 0;
  min-width: 40px;
}

.price-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-input {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.121569);
  border-radius: 8px;
  padding: 0 10px;
  height: 32px;
  min-width: 100px;
}

.currency-symbol {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.486275);
  margin: 0;
}

.price-field {
  border: none;
  outline: none;
  background: transparent;
  font-family: 'Inter Display', sans-serif;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.01em;
  color: rgba(0, 0, 0, 0.87451);
  width: 100%;
}

.currency-selector {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.121569);
  border-radius: 8px;
  padding: 0 10px;
  height: 32px;
  cursor: pointer;
  transition: all 0.2s;
}

.currency-selector:hover {
  border-color: #d0d0d0;
}

.currency-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.87451);
  margin: 0;
}

.quick-add-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.quick-add-btn {
  padding: 4px 8px;
  border: 1px solid rgba(0, 0, 0, 0.121569);
  border-radius: 6px;
  background: white;
  font-size: 12px;
  font-weight: 500;
  color: #202020;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-add-btn:hover {
  background: #f5f5f5;
}

.more-btn {
  padding: 4px 8px;
  border: none;
  background: transparent;
  font-size: 12px;
  font-weight: 500;
  color: #1754D8;
  cursor: pointer;
  transition: all 0.2s;
}

.more-btn:hover {
  color: #1441b8;
}

/* Right Panel */
.right-panel {
  background: white;
  border-radius: 0;
  padding: 24px;
  box-shadow: none;
  height: 100%;
  overflow-y: auto;
}

.preview-tabs {
  display: flex;
  gap: 0;
  margin-bottom: 24px;
  border-bottom: 1px solid #e8e8e8;
}

.preview-tab {
  padding: 12px 16px;
  border: none;
  background: transparent;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.preview-tab.active {
  color: #1754D8;
  border-bottom-color: #1754D8;
}

.preview-tab:hover:not(.active) {
  color: #333;
}

/* Contract Preview */
.contract-preview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.contract-card {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.contract-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.contract-title-text {
  font-size: 16px;
  font-weight: 500;
  color: #202020;
  margin: 0;
}

.contract-amount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.amount-text {
  font-size: 24px;
  font-weight: 500;
  color: #202020;
  margin: 0;
}

.pay-now-text {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.download-button {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.121569);
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #202020;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.download-button:hover {
  background: #f5f5f5;
}

.contract-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contract-number {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contract-label {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.contract-value {
  font-size: 14px;
  font-weight: 500;
  color: #202020;
  margin: 0;
}

.contract-description {
  margin: 16px 0;
}

.description-text {
  font-size: 14px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.87451);
  margin: 0;
}

.pay-now-button {
  background: #1754D8;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

.pay-now-button:hover {
  background: #1441b8;
}

/* Receipt Card */
.receipt-card {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.receipt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.receipt-title {
  font-size: 16px;
  font-weight: 500;
  color: #202020;
  margin: 0;
}

.receipt-date {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.receipt-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.receipt-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.receipt-item.total {
  padding-top: 12px;
  border-top: 1px solid #e8e8e8;
  font-weight: 500;
}

.item-name {
  font-size: 14px;
  color: #202020;
  margin: 0;
}

.item-amount {
  font-size: 14px;
  color: #202020;
  margin: 0;
}

.receipt-footer {
  display: flex;
  justify-content: flex-end;
}

.preview-label {
  font-size: 12px;
  color: #666;
  margin: 0;
}

/* Additional styling for exact match */
.contract-card {
  position: relative;
}

.contract-card::after {
  content: '';
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: #f0f0f0;
  border-radius: 8px;
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2Z' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M14 2V8H20' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M16 13H8' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M16 17H8' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M10 9H8' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: 24px 24px;
  background-position: center;
  background-repeat: no-repeat;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .two-panel-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .right-panel {
    position: static;
  }
}

@media (max-width: 768px) {
  .invoice-header {
    padding: 16px 20px;
  }
  
  .two-panel-layout {
    padding: 20px;
  }
  
  .left-panel,
  .right-panel {
    padding: 16px;
  }
}
