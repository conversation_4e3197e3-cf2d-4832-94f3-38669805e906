/* Contract Builder Styles */

.create-contract-container {
  position: relative;
  min-height: 100vh;
  background: var(--muted-background) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.create-contract-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.05;
  z-index: 0;
}

html.dark .create-contract-background,
html[data-theme="dark"] .create-contract-background,
[data-theme="dark"] .create-contract-background {
  opacity: 0.02;
}

.create-contract-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

/* Header Styles */
.create-contract-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 16px 0;
}

.create-contract-breadcrumb {
  display: flex;
  align-items: center;
}

.breadcrumb-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-back {
  margin-right: 8px;
}

.close-icon {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 18px;
}

.close-icon:hover {
  color: #374151;
}

.breadcrumb-path {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-text {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.breadcrumb-text.active {
  color: #111827;
  font-weight: 600;
}

.breadcrumb-chevron {
  display: flex;
  align-items: center;
  color: #9ca3af;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Primary Button */
.primary-button {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
}

.primary-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.content-container {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 8px;
}

.button-text {
  font-weight: 600;
  font-size: 14px;
}

.sparkles-left,
.sparkles-right {
  position: absolute;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  opacity: 0.8;
  animation: sparkle 2s infinite;
}

.sparkles-left {
  top: 8px;
  left: 12px;
  animation-delay: 0s;
}

.sparkles-right {
  bottom: 8px;
  right: 12px;
  animation-delay: 1s;
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
}

/* Main Content Layout */
.main-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 32px;
  align-items: start;
}

.form-panel {
  background: var(--card-background) !important;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px var(--shadow-medium);
  border: 1px solid var(--border-color) !important;
}

.preview-panel {
  background: var(--card-background) !important;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px var(--shadow-medium);
  border: 1px solid var(--border-color) !important;
  position: sticky;
  top: 24px;
}

/* Dark mode specific overrides */
html.dark .form-panel,
html.dark .preview-panel,
html[data-theme="dark"] .form-panel,
html[data-theme="dark"] .preview-panel,
[data-theme="dark"] .form-panel,
[data-theme="dark"] .preview-panel {
  background: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

/* Form Sections */
.customer-section,
.product-section,
.payment-collection-section,
.price-type-section,
.advanced-options-section,
.payment-options-section,
.description-section {
  margin-bottom: 24px;
}

.text-input {
  margin-bottom: 16px;
}

.label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  display: block;
}

.label-text {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.root {
  position: relative;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--input-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.2s ease;
}

.input-container:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px var(--focus-ring);
}

.placeholder {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-size: 14px;
  color: #111827;
}

.placeholder::placeholder {
  color: #9ca3af;
}

.chevron-down {
  color: #9ca3af;
  font-size: 12px;
  cursor: pointer;
  padding: 4px;
}

/* Frequently Used Section */
.frequently-used-section {
  margin-top: 16px;
}

.frequently-used-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.frequently-used-buttons {
  margin-bottom: 16px;
}

.frequently-used-row {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.frequent-button {
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.frequent-button:hover {
  background: #e5e7eb;
  border-color: #d1d5db;
}

.content-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.plus {
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 8px;
  font-weight: bold;
}

/* Payment Collection Section */
.payment-collection-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.payment-collection-header {
  background: #f9fafb;
  padding: 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
}

.payment-collection-header:hover {
  background: #f3f4f6;
}

.payment-collection-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.payment-collection-chevron {
  color: #9ca3af;
  font-size: 12px;
}

.payment-collection-content {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.payment-collection-content-inner {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.radio-item:hover {
  background: #f9fafb;
}

.radio-container {
  display: flex;
  align-items: center;
}

.radio {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.radio:not(.unselected) {
  border-color: #667eea;
  background: #667eea;
}

.radio.unselected {
  border-color: #d1d5db;
  background: white;
}

.indicator {
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.radio:not(.unselected) .indicator {
  opacity: 1;
}

.radio-text {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.radio-text.autocharge {
  color: #667eea;
}

.due-date-input {
  margin-left: 28px;
}

/* Price Type Section */
.price-type-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.price-type-section.expanded {
  border-color: #667eea;
}

.price-type-header {
  background: #f9fafb;
  padding: 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
}

.price-type-header:hover {
  background: #f3f4f6;
}

.price-type-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-type-text {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.price-type-chevron {
  color: #9ca3af;
  font-size: 12px;
}

.price-input-section {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  display: none;
}

.price-input-section.expanded {
  display: block;
}

.segmented-control {
  display: flex;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 2px;
  margin-bottom: 16px;
}

.segmented-button {
  flex: 1;
  background: transparent;
  border: none;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.segmented-button.middle {
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
}

.segmented-button.active {
  background: white;
  color: #374151;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.price-input-row {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 12px;
  margin-bottom: 16px;
}

.price-input {
  flex: 1;
}

.currency-selector {
  min-width: 80px;
}

.quick-add-buttons {
  margin-top: 16px;
}

.quick-add-row {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-add-button {
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.quick-add-button:hover {
  background: #e5e7eb;
  border-color: #d1d5db;
}

.more-button {
  background: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.more-button:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.text {
  font-size: 12px;
  color: #6b7280;
}

/* Advanced Options & Payment Options */
.advanced-options-section,
.payment-options-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.advanced-options-header,
.payment-options-header {
  background: #f9fafb;
  padding: 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
}

.advanced-options-header:hover,
.payment-options-header:hover {
  background: #f3f4f6;
}

.advanced-options-title,
.payment-options-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.advanced-options-chevron,
.payment-options-chevron {
  color: #9ca3af;
  font-size: 12px;
}

/* Description Section */
.description-section textarea {
  min-height: 80px;
  resize: vertical;
}

/* Preview Panel */
.preview-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-tabs {
  display: flex;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 2px;
}

.preview-tab {
  flex: 1;
  background: transparent;
  border: none;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.preview-tab:not(.inactive) {
  background: white;
  color: #374151;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.preview-tab.inactive {
  background: transparent;
  color: #6b7280;
}

/* Invoice Preview Card */
.invoice-preview-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.invoice-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.invoice-preview-info {
  flex: 1;
}

.invoice-preview-from {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.invoice-preview-amount {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.invoice-preview-date {
  font-size: 12px;
  color: #6b7280;
}

.invoice-preview-icon {
  width: 32px;
  height: 32px;
  background: #667eea;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.invoice-preview-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.invoice-preview-buttons {
  display: flex;
  gap: 8px;
}

.download-button {
  flex: 1;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.download-button:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.invoice-preview-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-top: 1px solid #e5e7eb;
}

.invoice-preview-number {
  font-size: 11px;
  color: #9ca3af;
}

.invoice-preview-id {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.pay-now-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 16px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.pay-now-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Receipt Preview Card */
.receipt-preview-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.receipt-preview-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.receipt-preview-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 12px;
}

.receipt-preview-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.receipt-preview-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.receipt-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.receipt-item-label {
  font-size: 12px;
  color: #6b7280;
}

.receipt-item-value {
  font-size: 12px;
  color: #111827;
  font-weight: 500;
}

.receipt-total {
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
  margin-top: 8px;
}

.receipt-total-label {
  font-size: 10px;
  color: #9ca3af;
  text-align: center;
  line-height: 1.4;
}

/* Contract Preview Document */
.contract-preview-document {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.contract-preview-header {
  background: #f9fafb;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.contract-preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.contract-preview-subtitle {
  font-size: 12px;
  color: #6b7280;
}

.contract-preview-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.contract-document-content {
  font-size: 12px;
  line-height: 1.5;
  color: #374151;
}

.contract-document-content h3 {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 16px 0 8px 0;
  padding-bottom: 4px;
  border-bottom: 1px solid #e5e7eb;
}

.contract-document-content p {
  margin: 8px 0;
}

.contract-document-content ul {
  margin: 8px 0;
  padding-left: 16px;
}

.contract-document-content li {
  margin: 4px 0;
}

.contract-preview-actions {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 8px;
}

.preview-action-button {
  flex: 1;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.preview-action-button:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.preview-action-button.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.preview-action-button.secondary:hover {
  background: #e5e7eb;
  transform: none;
}

/* Email Preview Document */
.email-preview-document {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.email-preview-header {
  background: #f9fafb;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.email-preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.email-preview-subtitle {
  font-size: 12px;
  color: #6b7280;
}

.email-preview-content {
  padding: 16px;
}

.email-meta {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.email-field {
  display: flex;
  margin-bottom: 6px;
}

.email-field:last-child {
  margin-bottom: 0;
}

.email-label {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  min-width: 60px;
  margin-right: 8px;
}

.email-value {
  font-size: 12px;
  color: #6b7280;
  flex: 1;
}

.email-body {
  font-size: 12px;
  line-height: 1.5;
  color: #374151;
}

.email-body p {
  margin: 8px 0;
}

.email-body ul {
  margin: 8px 0;
  padding-left: 16px;
}

.email-body li {
  margin: 4px 0;
}

.email-cta {
  margin: 16px 0;
  text-align: center;
}

.email-cta-button {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.email-cta-button:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

/* Preview Footer */
.preview-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.preview-footer-text {
  font-size: 11px;
  color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .preview-panel {
    position: static;
  }
}

@media (max-width: 768px) {
  .create-contract-content {
    padding: 16px;
  }
  
  .create-contract-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .frequently-used-row,
  .quick-add-row {
    flex-direction: column;
  }
  
  .price-input-row {
    grid-template-columns: 1fr;
  }
}
